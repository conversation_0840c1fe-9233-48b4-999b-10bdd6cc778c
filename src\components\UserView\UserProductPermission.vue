<script setup lang="tsx">
import { ref, shallowRef, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Repos, type MomUser, type LegacyFundInfo } from '../../../../xtrade-sdk/dist';
import VirtualizedTable from '../common/VirtualizedTable.vue';
import ProductSelect from '../common/ProductSelect.vue';
import type { ColumnDefinition, RowAction } from '@/types';
import { formatNumber, navCol, thousands, thousandsCol } from '@/script';

interface CellRenderParam {
  rowData: LegacyFundInfo;
  cellData: any;
}

const { user } = defineProps<{
  user?: MomUser;
}>();

// 仓库实例
const adminRepo = new Repos.AdminRepo();
const governanceRepo = new Repos.GovernanceRepo();

// 响应式数据
const userProducts = shallowRef<LegacyFundInfo[]>([]);
const allProducts = shallowRef<LegacyFundInfo[]>([]);
const selectedProductForShare = ref<string>('');

function renderShareCol(params: CellRenderParam) {
  const { fundAccounts } = params.rowData;
  if (!fundAccounts) return <span>--</span>;
  const share = fundAccounts.reduce((acc, cur) => acc + cur.fundShare, 0);
  return <span>{thousands(share)}</span>;
}

function renderWithNull() {
  return <span>--</span>;
}

// 用户产品表格列定义
const userProductColumns: ColumnDefinition<LegacyFundInfo> = [
  {
    key: 'fundName',
    title: '基金名字',
    width: 180,
    sortable: true,
    fixed: true,
  },
  {
    key: 'navRealTime',
    title: '最新净值',
    width: 90,
    sortable: true,
    align: 'right',
    cellRenderer: navCol,
    textRenderer: (val: any) => formatNumber(val),
  },
  {
    key: 'nav',
    title: '累计净值',
    width: 90,
    sortable: true,
    align: 'right',
    cellRenderer: navCol,
    textRenderer: (val: any) => formatNumber(val),
  },
  {
    key: 'fundAccounts',
    title: '产品份额',
    width: 90,
    sortable: true,
    align: 'right',
    cellRenderer: renderShareCol,
    textRenderer: (cellData, rowData: LegacyFundInfo) => {
      const share = rowData.fundAccounts?.reduce((acc, cur) => acc + cur.fundShare, 0) || 0;
      return thousands(share);
    },
  },
  {
    key: 'balance',
    title: '总资产',
    width: 150,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  {
    key: 'id',
    title: '收益率',
    width: 90,
    sortable: true,
    align: 'right',
    cellRenderer: renderWithNull,
  },
  {
    key: 'id',
    title: '总负债',
    width: 90,
    sortable: true,
    align: 'right',
    cellRenderer: renderWithNull,
  },
  {
    key: 'marketValue',
    title: '证券总资产',
    width: 90,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  {
    key: 'marketValue',
    title: '证券总市值',
    width: 90,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  {
    key: 'marketValue',
    title: '股票总市值',
    width: 90,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
];

// 用户产品行操作
const userProductRowActions: RowAction<LegacyFundInfo>[] = [
  {
    label: '取消分享',
    type: 'danger',
    onClick: (row: LegacyFundInfo) => {
      handleUnshareProduct(row);
    },
  },
];

// 加载分享给用户的产品列表
const loadUserProducts = async () => {
  if (!user?.id) return;

  const { errorCode, errorMsg, data } = await adminRepo.QueryUserProducts(user.id);
  if (errorCode === 0) {
    userProducts.value = (data || []).map(fund => {
      const matched = allProducts.value.find(item => item.id == fund.id);
      if (matched) {
        return matched;
      } else {
        return fund;
      }
    });
  } else {
    ElMessage.error(errorMsg || '加载用户产品失败');
  }
};

// 加载所有产品列表
const loadAllProducts = async () => {
  const { errorCode, errorMsg, data } = await governanceRepo.QueryProducts();
  if (errorCode === 0) {
    allProducts.value = data || [];
  } else {
    ElMessage.error(errorMsg || '加载产品列表失败');
  }
};

// 分享产品给用户
const handleShareProduct = async (id: string | undefined) => {
  if (!id || !user?.id) return;

  const fund = allProducts.value.find(item => item.id == id)!;
  const { errorCode, errorMsg } = await adminRepo.ShareUserProducts(user.id, [
    {
      id: Number(fund.id),
      fundType: fund.fundType,
      fundName: fund.fundName,
    },
  ]);

  if (errorCode === 0) {
    ElMessage.success('分享成功');
    selectedProductForShare.value = '';
    await loadUserProducts();
  } else {
    ElMessage.error(errorMsg || '分享失败');
  }
};

// 取消分享产品
const handleUnshareProduct = async (product: LegacyFundInfo) => {
  if (!user?.id) return;

  const { errorCode, errorMsg } = await adminRepo.UnshareUserProducts(user.id, product.id);

  if (errorCode === 0) {
    ElMessage.success('取消分享成功');
    await loadUserProducts();
  } else {
    ElMessage.error(errorMsg || '取消分享失败');
  }
};

// 监听用户变化
watch(
  () => user?.id,
  async newUserId => {
    if (newUserId) {
      await loadAllProducts();
      loadUserProducts();
    }
  },
  { immediate: true },
);
</script>

<template>
  <div class="user-product-permission" p-4>
    <!-- 产品选择和分享 -->
    <div w-200 mt-10>
      <ProductSelect
        ref="productSelectRef"
        v-model="selectedProductForShare"
        :exclude-ids="userProducts.map(p => p.id)"
        :org-id="user?.orgId"
        from="user"
        @change="handleShareProduct"
      />
    </div>

    <!-- 分享给用户的产品列表 -->
    <div class="user-products-section" mb-6>
      <h3 class="section-title" mb-3>分享给用户的产品</h3>
      <div h-300>
        <VirtualizedTable
          :columns="userProductColumns"
          :data="userProducts"
          :row-actions="userProductRowActions"
          :row-action-width="120"
          :show-toolbar="false"
          fixed
        />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
