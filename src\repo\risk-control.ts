import { BaseRepo } from '../modules/base-repo';
import { EntityRiskTemplateInfo, KindOfAsset, RiskIndicator, RiskMessage, RiskRule, RiskTemplate, TemplateBoundOnAccount, TemplateBoundOnProduct } from '../types/table/risk-control';

const BaseUrl = '../v4/risk';

export class RiskControlRepo extends BaseRepo {

    constructor() {
        super();
    }

    /**
     * 查询所有风控模板
     */
    async QueryTemplates() {
        return await this.assist.Get<RiskTemplate[]>(`${BaseUrl}/template/all`);
    }

    /**
     * 创建风控模板
     */
    async CreateTemplate(template: RiskTemplate) {
        return await this.assist.Post<[]>(`${BaseUrl}/template`, {}, template);
    }

    /**
     * 更新风控模板
     */
    async UpdateTemplate(template: RiskTemplate) {
        return await this.assist.Put<[]>(`${BaseUrl}/template`, {}, template);
    }

    /**
     * 克隆风控模板
     * @param template_id 用来克隆的模板id
     * @param template_name 克隆出的模板名称
     * @param isFull 是否全量克隆（为true代表同时复制基准模版的所有产品和账号的绑定关系）
     */
    async CloneTemplate(template_id: number, template_name: string, isFull: boolean) {
        return await this.assist.Get<[]>(`${BaseUrl}/template/clone`, { template_id, template_name, isFull });
    }

    /**
     * 删除风控模板
     */
    async DeleteTemplate(id: number) {
        return await this.assist.Delete<[]>(`${BaseUrl}/template`, { id });
    }

    /**
     * 绑定风控模板到产品
     */
    async BindTemplate2Products(template_id: number, ids: string[]) {
        return await this.assist.Post<[]>(`${BaseUrl}/template/bindFund`, { template_id }, { template_id, ids });
    }

    /**
     * 绑定风控模板到账号
     */
    async BindTemplate2Accounts(template_id: number, ids: string[]) {
        return await this.assist.Post<[]>(`${BaseUrl}/template/bindAccount`, { template_id }, { template_id, ids });
    }

    /**
     * 查询某个风控模版被绑定到的产品
     */
    async QueryTemplateBoundProducts(template_id: number) {
        return await this.assist.Get<TemplateBoundOnProduct[]>(`${BaseUrl}/template/findByFund`, { template_id });
    }

    /**
     * 查询某个风控模版被绑定到的账号
     */
    async QueryTemplateBoundAccounts(template_id: number) {
        return await this.assist.Get<TemplateBoundOnAccount[]>(`${BaseUrl}/template/findByAccount`, { template_id });
    }

    /**
     * 查询某个产品所绑定的所有模板，以及每个模板下所包含的风控规则
     */
    async QueryProductTemplates(fund_id: number | string) {
        return await this.assist.Get<EntityRiskTemplateInfo[]>(`${BaseUrl}/fund/findByFundId`, { fund_id });
    }

    /**
     * 查询某个账号所绑定的所有模板，以及每个模板下所包含的风控规则
     */
    async QueryAccountTemplates(account_id: number | string) {
        return await this.assist.Get<EntityRiskTemplateInfo[]>(`${BaseUrl}/fund/findByAccountId`, { account_id });
    }

    /**
     * 查询所有风控指标
     */
    async QueryIndicators() {
        return await this.assist.Get<RiskIndicator[]>(`${BaseUrl}/indicator/all`);
    }

    /**
     * 查询所有风控规则
     */
    async QueryRules() {
        return await this.assist.Get<RiskRule[]>(`${BaseUrl}/rule/all`);
    }

    /**
     * 创建风控规则
     */
    async CreateRule(rule: RiskRule) {
        return await this.assist.Post<[]>(`${BaseUrl}/rule`, {}, rule);
    }

    /**
     * 更新风控规则
     */
    async UpdateRule(rule: RiskRule) {
        return await this.assist.Put<[]>(`${BaseUrl}/rule`, {}, rule);
    }

    /**
     * 删除风控规则
     */
    async DeleteRule(id: number) {
        return await this.assist.Delete<[]>(`${BaseUrl}/rule`, { id });
    }

    /**
     * 查询资产范围
     */
    async QueryAssetScopes() {
        return await this.assist.Get<KindOfAsset[]>(`${BaseUrl}/kind/all`);
    }

    /**
     * 查询已产生的预警消息
     */
    async QueryRiskMessages() {
        return await this.assist.Get<RiskMessage[]>(`${BaseUrl}/message/all`);
    }
}
