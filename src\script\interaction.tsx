/**
 * 删除确认对话框
 */
export const deleteConfirm = async (title: string, message: string): Promise<boolean> => {
  return new Promise(resolve => {
    import('element-plus').then(({ ElMessageBox }) => {
      ElMessageBox({
        title,
        showCancelButton: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'delete',
        type: 'error',
        showClose: false,
        message,
        buttonSize: 'large',
        dangerouslyUseHTMLString: true,
        draggable: true,
        icon: () => <i class="fs-24 iconfont icon-warning-circle-fill" />,
      })
        .then(() => resolve(true))
        .catch(() => resolve(false));
    });
  });
};

export interface ChooseConfirmDialogOption {
  confirmButtonText: string;
  cancelButtonText: string;
  confirmButtonClass?: string;
  cancelButtonClass?: string;
}

export type ChooseConfirmResult = 'confirm' | 'cancel' | 'close';

/**
 * 选择确认对话框
 */
export const chooseConfirm = async (
  title: string,
  message: string,
  options: ChooseConfirmDialogOption,
): Promise<ChooseConfirmResult> => {
  return new Promise(resolve => {
    import('element-plus').then(({ ElMessageBox }) => {
      ElMessageBox({
        title,
        message,
        confirmButtonText: options.confirmButtonText,
        confirmButtonClass: options.confirmButtonClass || 'primary',
        showCancelButton: true,
        cancelButtonText: options.cancelButtonText,
        type: 'warning',
        buttonSize: 'large',
        showClose: true,
        dangerouslyUseHTMLString: true,
        draggable: true,
        distinguishCancelAndClose: true,
        icon: () => <i class="fs-36 iconfont icon-warning-filling" />,
      })
        .then(() => {
          resolve('confirm');
        })
        .catch((reason: ChooseConfirmResult) => {
          resolve(reason);
        });
    });
  });
};
