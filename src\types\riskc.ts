import type { IdcComponentNameDef } from '@/components/RiskTemplateView/ComponentNameDef';

/**
 * 任何指标风控参数基类
 */
export interface AnyIndicatorRiskParamObject {
  classType: IdcComponentNameDef;
}

/**
 * 指标触发项
 */
export interface IndicatorTrigger {
  /**
   * 比较符
   */
  comparer: number;

  /**
   * 阈值
   */
  threshold: number;

  /**
   * 阈值单位
   */
  threholdUnit: string;

  /**
   * 触发项采取动作：1预警 2阻止
   */
  action: number;
}

/**
 * 资产作用域设置
 */
export interface AssetScopeSetting {
  /**
   * 资产类别代码
   */
  kindCodes: string[];

  /**
   * 叠加条件
   */
  overlaies: number[];
}

/**
 * 风控预警项通用配置
 */
export interface CommonRiskAlertConfig {
  expression: number | null;
  value: number | null;
  alertType: number;
}

/**
 * 交易频率控制参数
 */
export interface TradeFrequencyConfig extends CommonRiskAlertConfig {
  second: number;
}

/**
 * 市值配置参数
 */
export interface MarketValueConfig extends CommonRiskAlertConfig {
  nav: number;
  navExpression: number;
}

/**
 * 上下文指标信息
 */
export interface ContextualIndicatorInfo {
  indicatorId: number;
  indicatorName: string;
  componentName: string;
}
