import { BaseRepo } from '../modules/base-repo';
import {
  TradeClassification,
  TradeClassificationMember,
  TradeClassificationType,
} from '../types/table/classification';

const ClassBaseUrl = '../v4/risk/tradeClassification';
const ClassMemberBaseUrl = '../v4/risk/tradeClassificationMember/member';
const ClassMemberBatchBaseUrl = '../v4/risk/tradeClassificationMemberBatch/member';
const FundBaseUrl = '../v4/risk/fundGroup';
const AccountBaseUrl = '../v4/risk/accountGroup';

export class ClassificationRepo extends BaseRepo {
  constructor() {
    super();
  }

  /**
   * 根据ID获取交易分类
   */
  async getTradeClassifications(type: TradeClassificationType) {
    const baseUrl =
      type == TradeClassificationType.ProductGroup
        ? FundBaseUrl
        : type == TradeClassificationType.AccountGroup
        ? AccountBaseUrl
        : ClassBaseUrl;
    return this.assist.Get<TradeClassification[]>(`${baseUrl}/all`);
  }

  /**
   * 创建交易分类
   */
  async createTradeClassification(data: TradeClassification) {
    delete (data as any).id;
    return this.assist.Post<TradeClassification>(`${ClassBaseUrl}`, {}, data);
  }

  /**
   * 更新交易分类
   */
  async updateTradeClassification(data: TradeClassification) {
    return this.assist.Put<TradeClassification>(`${ClassBaseUrl}`, {}, data);
  }

  /**
   * 删除交易分类
   */
  async deleteTradeClassification(tradeClassificationId: number) {
    return this.assist.Delete<TradeClassification>(`${ClassBaseUrl}`, { tradeClassificationId });
  }

  /**
   * 根据ID获取交易分类成员
   */
  async getTradeClassificationMembers(tradeClassificationId: number) {
    return this.assist.Get<TradeClassificationMember[]>(`${ClassMemberBaseUrl}`, {
      tradeClassificationId,
    });
  }

  /**
   * 创建交易分类成员
   */
  async createTradeClassificationMember(
    tradeClassificationId: number,
    data: TradeClassificationMember,
  ) {
    delete (data as any).id;
    const { memberCode, memberName } = data;
    return this.assist.Post<TradeClassificationMember>(
      `${ClassMemberBaseUrl}/../mount`,
      { tradeClassificationId, memberCode, memberName },
      data,
    );
  }

  /**
   * 更新交易分类成员
   */
  async updateTradeClassificationMember(
    tradeClassificationId: number,
    data: TradeClassificationMember,
  ) {
    return this.assist.Put<TradeClassificationMember>(
      `${ClassMemberBaseUrl}`,
      { tradeClassificationId },
      data,
    );
  }

  /**
   * 删除交易分类成员
   */
  async deleteTradeClassificationMember(id: number | number[]) {
    if (Array.isArray(id)) {
      return this.assist.Delete<TradeClassificationMember>(`${ClassMemberBatchBaseUrl}`, {
        tradeClassificationMemberIds: id.join(','),
      });
    } else {
      return this.assist.Delete<TradeClassificationMember>(`${ClassMemberBaseUrl}`, {
        tradeClassificationMemberId: id,
      });
    }
  }
}
