<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import ClassDialog from './ClassDialog.vue';
import { ref, watch } from 'vue';
import { TableV2SortOrder, ElMessage } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { deepClone, formatDateTime, remove, renderLabel } from '@/script';
import { deleteConfirm } from '@/script/interaction';

import {
  Repos,
  type TradeClassification,
  TradeClassificationType,
  TradeClassificationTypes,
} from '../../../../xtrade-sdk/dist';

interface CellRenderParam {
  rowData: TradeClassification;
  cellData: any;
}

const repoInstance = new Repos.ClassificationRepo();

// 基础列定义
const columns: ColumnDefinition<TradeClassification> = [
  { key: 'name', title: '分类名称', width: 200, minWidth: 200, sortable: true },
  {
    key: 'type',
    title: '类型',
    width: 150,
    minWidth: 150,
    sortable: true,
    cellRenderer: renderClassType,
  },
  { key: 'createUserName', title: '创建人', width: 100, minWidth: 100, sortable: true },
  {
    key: 'updateTime' as any,
    title: '更新时间',
    width: 100,
    sortable: true,
    cellRenderer: formatDate,
  },
  { key: 'description', title: '备注', width: 200, minWidth: 200 },
];

// 行操作
const rowActions: RowAction<TradeClassification>[] = [
  {
    label: '编辑',
    icon: 'edit',
    show: isEditAllowed,
    onClick: row => {
      editRow(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    show: isEditAllowed,
    onClick: row => {
      deleteRow(row);
    },
  },
];

const records = ref<TradeClassification[]>([]);

// 对话框相关
const dialogVisible = ref(false);
const editingClassification = ref<TradeClassification | undefined>();

const { classType } = defineProps<{
  classType: TradeClassificationType;
}>();

watch(
  () => classType,
  () => {
    request();
  },
  { immediate: true },
);

const emitter = defineEmits<{
  classChange: [classId: number | null, className: string | null];
}>();

function isEditAllowed(data: TradeClassification) {
  return data.orgId != -1;
}

function renderClassType(params: CellRenderParam) {
  return <span>{renderLabel(params.cellData, TradeClassificationTypes)}</span>;
}

function formatDate(params: CellRenderParam) {
  return <span>{formatDateTime(params.cellData)}</span>;
}

// 新建分类
const handleCreate = () => {
  editingClassification.value = undefined;
  dialogVisible.value = true;
};

// 编辑分类
function editRow(row: TradeClassification) {
  editingClassification.value = deepClone(row);
  dialogVisible.value = true;
}

// 删除分类
async function deleteRow(row: TradeClassification) {
  const result = await deleteConfirm('删除确认', `是否删除分类： ${row.name}？`);
  if (result !== true) {
    return;
  }

  const resp = await repoInstance.deleteTradeClassification(row.id);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success('已删除');
    const list = records.value;
    remove(list, x => x.id == row.id);
    const first = list.length > 0 ? list[0] : null;
    emitter('classChange', first?.id || null, first?.name || null);
  } else {
    ElMessage.error(`删除失败：${errorCode}/${errorMsg}`);
  }
}

function handleRowClick(row: TradeClassification) {
  emitter('classChange', row.id, row.name);
}

// 对话框成功回调
const handleDialogSuccess = async () => {
  await request();
};

async function request() {
  const list = (await repoInstance.getTradeClassifications(classType)).data || [];
  records.value = list;
  const first = list.length > 0 ? list[0] : null;
  emitter('classChange', first?.id || null, first?.name || null);
}
</script>

<template>
  <div w-full h-full pr-10>
    <VirtualizedTable
      ref="tableRef"
      :sort="{ key: 'id', order: TableV2SortOrder.DESC }"
      :columns="columns"
      :data="records"
      :row-actions="rowActions"
      :row-action-width="150"
      @row-click="handleRowClick"
      select
      fixed
      show-index
    >
      <template #actions>
        <div class="actions" flex aic>
          <el-button type="primary" @click="handleCreate">
            <i class="iconfont icon-add-new" mr-5></i>
            <span>新建</span>
          </el-button>
        </div>
      </template>
    </VirtualizedTable>
  </div>

  <!-- 分类编辑对话框 -->
  <ClassDialog
    v-model="dialogVisible"
    :class-type="classType"
    :classification="editingClassification"
    @success="handleDialogSuccess"
  />
</template>

<style scoped></style>
