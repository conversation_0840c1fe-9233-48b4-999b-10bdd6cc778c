<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onBeforeUnmount, onMounted, shallowRef, useTemplateRef, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { positionColumns } from './shared/columnDefinitions';
import { putRow } from '@/script';
import type { AccountInfo, ColumnDefinition } from '@/types';
import VirtualizedTable from '../VirtualizedTable.vue';
import { RecordService, TradingService } from '@/api';
import type {
  PositionInfo,
  SocketDataPackage,
  LegacyFundInfo,
} from '../../../../../xtrade-sdk/dist';
import { TableV2SortOrder } from 'element-plus';

// 定义组件属性
const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : LegacyFundInfo;
}>();

// 基础列定义
const baseColumns: ColumnDefinition<PositionInfo> = [
  positionColumns.instrument,
  positionColumns.instrumentName,
  positionColumns.assetType,
  positionColumns.direction,
  positionColumns.yesterdayPosition,
  positionColumns.todayPosition,
  positionColumns.frozenVolume,
  positionColumns.avgPrice,
  positionColumns.marketValue,
  positionColumns.floatProfit,
  positionColumns.closeProfit,
  positionColumns.usedCommission,
  positionColumns.usedMargin,
  positionColumns.updateTime,
];

// 根据类型动态生成列
const columns = computed(() => {
  const cols = [...baseColumns];
  if (type === 'product') {
    cols.splice(2, 0, positionColumns.accountName);
  }
  return cols;
});

const tableRef = useTemplateRef('tableRef');
const positions = shallowRef<PositionInfo[]>([]);

// 监听账户/产品变化，重新获取持仓数据
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      fetchPositions();
    }
  },
  { deep: true },
);

// 初始化数据
onMounted(() => {
  if (activeItem) {
    fetchPositions();
  }
  TradingService.subscribePositionChange(handlePositionChange);
});

onBeforeUnmount(() => {
  TradingService.unsubscribePositionChange(handlePositionChange);
});

/** 监听持仓变化 */
const handlePositionChange = (data: SocketDataPackage<PositionInfo>) => {
  const { body } = data;
  if (body) {
    putRow(body, positions, 'instrument');
  }
};

// 获取持仓数据
const fetchPositions = async () => {
  if (!activeItem) return;
  const data = await RecordService.getTodayPositions(activeItem.id);
  // console.log(data);

  positions.value = data;
};

// 平仓选中的持仓
const closeSelectedPositions = () => {
  if (tableRef.value?.selectedRows.length === 0) {
    ElMessage.warning('请选择持仓');
    return;
  }

  ElMessage.warning('待实现');
  // TODO: 实现平仓逻辑
  console.log('平仓选中的持仓:', tableRef.value?.selectedRows);
};
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    select
    fixed
    :columns="columns"
    :data="positions"
    :sort="{ key: 'updateTime', order: TableV2SortOrder.DESC }"
  >
    <template #actions>
      <div class="actions" flex aic>
        <el-button @click="fetchPositions" size="small" color="var(--g-primary)">刷新</el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
