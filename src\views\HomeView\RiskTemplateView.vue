<script setup lang="ts">
import TemplateList from '@/components/RiskTemplateView/TemplateList.vue';
import IndicatorTree from '@/components/RiskTemplateView/IndicatorTree.vue';
import IndicatorList from '@/components/RiskTemplateView/IndicatorList.vue';
import CommonSharedIndicator from '@/components/RiskTemplateView/IndicatorConfig/CommonSharedIndicator.vue';
import type { RiskIndicator, RiskRule, RiskTemplate } from '../../../../xtrade-sdk/dist';
import { computed, reactive, ref, useTemplateRef } from 'vue';
import { deepClone } from '@/script';
import type { ContextualIndicatorInfo } from '@/types/riskc';

const isIndicatorCollapsed = ref(false);
const $tree = useTemplateRef('$tree');
const $tmpl = useTemplateRef('$tmpl');
const $idcList = useTemplateRef('$idcList');

const states = reactive({
  /** 当前选中的风控模板 */
  template: null as RiskTemplate | null,
  /** 当前全量指标id与名称键值队 */
  indicatorId2Names: [] as ContextualIndicatorInfo[],
  /** 当前选中的指标树节点 */
  selectedIndicator: null as RiskIndicator | null,
  /** 当前选中的风控规则 */
  selctedRule: null as RiskRule | null,
});

/**
 * 当前风控规则，所依附的风控指标，其指标基础信息
 */
const indicatorInfo = computed(() => {
  const { selctedRule, indicatorId2Names } = states;
  const matched = indicatorId2Names.find(x => x.indicatorId == selctedRule?.indicatorId);
  return matched;
});

/**
 * 当前选择的指标树节点，其指标基础信息
 */
const currentSelectedIndicatorInfo = computed(() => {
  const { selectedIndicator, indicatorId2Names } = states;
  const matched = indicatorId2Names.find(x => x.indicatorId == selectedIndicator?.id);
  return matched;
});

function handleTemplateChange(tmpl: RiskTemplate | null) {
  states.template = tmpl ? deepClone(tmpl) : null;
}

function handleIndicatorsDataChange(
  indicators: { indicatorId: number; indicatorName: string; componentName: string }[],
) {
  states.indicatorId2Names = indicators;
}

function handleNodeSelect(idc: RiskIndicator) {
  states.selectedIndicator = deepClone(idc);
}

function handleRulesDataChange(
  tmpl2Rules: { templateId: number; ruleIds: number[] }[],
  indicator2Count: { indicatorId: number; count: number }[],
) {
  $tmpl.value!.setRulesCount(tmpl2Rules);
  $tree.value!.setIndicatorsCount(indicator2Count);
}

function handleSelectRule(rule: RiskRule | null) {
  // 深度克隆，和原数据进行隔离
  states.selctedRule = rule ? deepClone(rule) : null;
}

function handleRuleSaved() {
  $idcList.value!.refresh();
}

function handleToggle() {
  isIndicatorCollapsed.value = !isIndicatorCollapsed.value;
}
</script>

<template>
  <div class="risk-template-view" flex>
    <div w-242 class="block">
      <TemplateList ref="$tmpl" @select="handleTemplateChange" @toggle="handleToggle" />
    </div>
    <div w-248 class="block" v-show="!isIndicatorCollapsed">
      <IndicatorTree ref="$tree" @report="handleIndicatorsDataChange" @select="handleNodeSelect" />
    </div>
    <div w-100 flex-1 flex flex-col>
      <div class="idc-rule-list" flex-1 min-h-100 jcc aic>
        <IndicatorList
          ref="$idcList"
          v-bind:context-template="states.template"
          v-bind:context-indicator="currentSelectedIndicatorInfo"
          @changed="handleRulesDataChange"
          @select="handleSelectRule"
        ></IndicatorList>
      </div>
      <template v-if="states.selctedRule">
        <template v-if="indicatorInfo?.componentName">
          <div h-580>
            <CommonSharedIndicator
              v-bind:context-rule="states.selctedRule"
              v-bind:context-indicator="indicatorInfo"
              @saved="handleRuleSaved"
            ></CommonSharedIndicator>
          </div>
        </template>
        <template v-else>
          <div h-550 flex jcc aic>
            <span fs-20 c-red>当前规则，没有预设参数模板！</span>
          </div>
        </template>
      </template>
    </div>
  </div>
</template>

<style scoped>
.block {
  height: 100%;
  overflow-y: hidden;
  background-color: var(--g-block-bg-2);
}
.idc-rule-list {
  :deep() {
    .el-table-v2__empty {
      display: none !important;
    }
  }
}
</style>
