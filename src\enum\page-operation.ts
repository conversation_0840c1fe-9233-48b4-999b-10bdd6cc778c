import type { MenuOperationDefinition } from '@/types';

/**
 * 页面操作
 */
const PageOperations: MenuOperationDefinition[] = [
  {
    menuId: 101,
    name: '产品管理',
    key: 'product_management',
    operations: [
      { name: '创建产品', key: 'create', method: 'GET', url: '/fund', type: '维护' },
      { name: '修改产品', key: 'edit', method: 'PUT', url: '/fund', type: '维护' },
      { name: '删除产品', key: 'delete', method: 'DELETE', url: '/fund', type: '维护' },
      { name: '下载产品', key: 'download', method: null, type: '维护' },
      { name: '列配置', key: 'column_config', method: null, type: '维护' },
      { name: '产品权益查看', key: 'view_equity', method: null, type: '查看' },
      { name: '账号数据查看', key: 'view_account_data', method: null, type: '查看' },
      { name: '持仓数据查看', key: 'view_position_data', method: null, type: '查看' },
      { name: '成交数据查看', key: 'view_trade_data', method: null, type: '查看' },
      { name: '指令数据查看', key: 'view_order_data', method: null, type: '查看' },
      { name: '账号配置', key: 'account_config', method: null, type: '配置' },
      { name: '人员配置', key: 'staff_config', method: null, type: '配置' },
      { name: '风控配置', key: 'risk_config', method: null, type: '配置' },
    ],
  },
  {
    menuId: 300,
    name: '产品概览',
    key: 'product_overview',
    operations: [],
  },
  {
    menuId: -1,
    name: '产品净值',
    key: 'product_net_value',
    operations: [],
  },
  {
    menuId: -1,
    name: '今日订单',
    key: 'today_orders',
    operations: [
      {
        name: '工具栏撤销全部',
        key: 'toolbar_cancel_all',
        method: 'TCP',
        functionCode: 10002,
        type: '操作',
      },
      {
        name: '工具栏撤销全部买单',
        key: 'toolbar_cancel_all_bids',
        method: 'TCP',
        functionCode: 10002,
        type: '操作',
      },
      {
        name: '工具栏撤销全部卖单',
        key: 'toolbar_cancel_all_asks',
        method: 'TCP',
        functionCode: 10002,
        type: '操作',
      },
      {
        name: '工具栏撤勾选',
        key: 'toolbar_cancel_selected',
        method: 'TCP',
        functionCode: 10002,
        type: '操作',
      },
      { name: '撤单条', key: 'cancel_order', method: 'TCP', functionCode: 10002, type: '操作' },
      { name: '添加单条', key: 'add_order', method: 'POST', url: '/order/add', type: '操作' },
      { name: '编辑单条', key: 'edit_order', method: 'POST', url: '/order/update', type: '操作' },
      {
        name: '删除单条',
        key: 'delete_order',
        method: 'DELETE',
        url: '/order/delete',
        type: '操作',
      },
    ],
  },
  {
    menuId: -1,
    name: '今日持仓',
    key: 'today_positions',
    operations: [
      { name: '添加调仓记录', key: 'add_position_adjustment_record', method: null, type: '操作' },
      { name: '添加单条', key: 'add_position', method: 'POST', url: '/position/add', type: '操作' },
      {
        name: '编辑单条',
        key: 'edit_position',
        method: 'POST',
        url: '/position/update',
        type: '操作',
      },
      {
        name: '删除单条',
        key: 'delete_position',
        method: 'DELETE',
        url: '/position/delete',
        type: '操作',
      },
    ],
  },
  {
    menuId: -1,
    name: '今日成交',
    key: 'today_trades',
    operations: [
      { name: '添加单条', key: 'add_trade', method: 'POST', url: '/trade/add', type: '操作' },
      { name: '编辑单条', key: 'edit_trade', method: 'POST', url: '/trade/update', type: '操作' },
      {
        name: '删除单条',
        key: 'delete_trade',
        method: 'DELETE',
        url: '/trade/delete',
        type: '操作',
      },
    ],
  },
  {
    menuId: -1,
    name: '历史订单',
    key: 'historical_orders',
    operations: [],
  },
  {
    menuId: -1,
    name: '历史持仓',
    key: 'historical_positions',
    operations: [],
  },
  {
    menuId: -1,
    name: '历史成交',
    key: 'historical_trades',
    operations: [],
  },
  {
    menuId: -1,
    name: '历史权益',
    key: 'historical_equity',
    operations: [],
  },
  {
    menuId: 102,
    name: '账号管理',
    key: 'account_management',
    operations: [
      { name: '创建账号', key: 'create', method: 'POST', url: '/account', type: '维护' },
      {
        name: '创建虚拟账号',
        key: 'create_virtual_account',
        method: 'POST',
        url: '/account',
        type: '维护',
      },
      { name: '修改账号', key: 'edit', method: 'PUT', url: '/account', type: '维护' },
      {
        name: '密码维护',
        key: 'password_maintenance',
        method: 'PUT',
        url: '/account',
        type: '维护',
      },
      {
        name: '配置终端',
        key: 'set_terminal',
        method: 'POST',
        url: '/account/bind-terminal',
        type: '维护',
      },
      { name: '删除账号', key: 'delete', method: 'DELETE', url: '/account', type: '维护' },
      { name: '下载账号列表', key: 'download', method: null, type: '维护' },
      { name: '列配置', key: 'column_config', method: null, type: '维护' },
      {
        name: '连通性测试',
        key: 'connect_disconnect_switch',
        method: 'GET',
        url: '/account/connection_test',
        type: '维护',
      },
      {
        name: '查看权益',
        key: 'view_equity',
        method: 'POST',
        url: '/account/prebalance',
        type: '配置',
      },
      { name: '出入金管理', key: 'fund_management', method: null, type: '配置' },
      { name: '费用管理', key: 'fee_management', method: null, type: '配置' },
      { name: '清算', key: 'settlement', method: null, type: '配置' },
      { name: '可用资金检查', key: 'available_fund_check', method: null, type: '配置' },
      { name: '持仓检查', key: 'position_check', method: null, type: '配置' },
      { name: '持仓查看', key: 'position_view', method: null, type: '配置' },
      { name: '风控', key: 'risk_control', method: null, type: '配置' },
      { name: '权益维护', key: 'equity_maintenance', method: null, type: '配置' },
      { name: '对比覆盖', key: 'compare_override', method: null, type: '配置' },
      { name: '资金覆盖', key: 'fund_override', method: null, type: '配置' },
      { name: '同步合约', key: 'sync_contract', method: null, type: '配置' },
      { name: '初始化', key: 'init_account', method: null, type: '配置' },
    ],
  },
  {
    menuId: -1,
    name: '出入金',
    key: 'fund_in_out',
    operations: [
      { name: '添加', key: 'add', method: 'POST', url: '/account/cashserial', type: '操作' },
    ],
  },
  {
    menuId: -1,
    name: '账号监控',
    key: 'account_monitoring',
    operations: [
      { name: '覆盖', key: 'override', method: 'POST', url: '/account/overlap', type: '操作' },
      {
        name: '成交检测',
        key: 'trade_detection',
        method: 'POST',
        url: '/account/check/trade',
        type: '操作',
      },
      {
        name: '委托检测',
        key: 'order_detection',
        method: 'POST',
        url: '/account/check/order',
        type: '操作',
      },
      {
        name: '资金检测',
        key: 'fund_detection',
        method: 'POST',
        url: '/account/check/balance',
        type: '操作',
      },
    ],
  },
  {
    menuId: -1,
    name: '权益查询',
    key: 'equity_query',
    operations: [],
  },
  {
    menuId: -1,
    name: '委托查询',
    key: 'order_query',
    operations: [],
  },
  {
    menuId: -1,
    name: '持仓查询',
    key: 'position_query',
    operations: [],
  },
  {
    menuId: -1,
    name: '成交查询',
    key: 'trade_query',
    operations: [],
  },
  {
    menuId: 400,
    name: '普通交易',
    key: 'normal_trading',
    operations: [
      { name: '现货竞价交易', key: 'spot_auction_trading', method: null, type: '交易' },
      { name: '信用交易', key: 'credit_trading', method: null, type: '交易' },
      { name: '可转债交易', key: 'convertible_bond_trading', method: null, type: '交易' },
      { name: '期货交易', key: 'futures_trading', method: null, type: '交易' },
    ],
  },
  {
    menuId: 401,
    name: '批量交易',
    key: 'batch_trading',
    operations: [
      { name: '现货竞价交易', key: 'spot_auction_trading', method: null, type: '交易' },
      { name: '信用交易', key: 'credit_trading', method: null, type: '交易' },
      { name: '可转债交易', key: 'convertible_bond_trading', method: null, type: '交易' },
      { name: '期货交易', key: 'futures_trading', method: null, type: '交易' },
    ],
  },
  {
    menuId: 402,
    name: '篮子算法交易',
    key: 'basket_algorithm_trading',
    operations: [
      { name: '现货竞价交易', key: 'spot_auction_trading', method: null, type: '交易' },
      { name: '信用交易', key: 'credit_trading', method: null, type: '交易' },
      { name: '可转债交易', key: 'convertible_bond_trading', method: null, type: '交易' },
      { name: '期货交易', key: 'futures_trading', method: null, type: '交易' },
      { name: '创建篮子', key: 'create_basket', method: null, type: '交易' },
      { name: '删除篮子', key: 'delete_basket', method: null, type: '交易' },
      { name: '篮子调仓', key: 'basket_rebalancing', method: null, type: '交易' },
    ],
  },
  {
    menuId: 403,
    name: '算法交易',
    key: 'algorithm_trading',
    operations: [
      { name: '现货竞价交易', key: 'spot_auction_trading', method: null, type: '交易' },
      { name: '信用交易', key: 'credit_trading', method: null, type: '交易' },
      { name: '可转债交易', key: 'convertible_bond_trading', method: null, type: '交易' },
      { name: '期货交易', key: 'futures_trading', method: null, type: '交易' },
      { name: '创建篮子', key: 'create_basket', method: null, type: '交易' },
      { name: '删除篮子', key: 'delete_basket', method: null, type: '交易' },
      { name: '篮子调仓', key: 'basket_rebalancing', method: null, type: '交易' },
    ],
  },
  {
    menuId: -1,
    name: '文件扫单',
    key: 'file_scanning',
    operations: [],
  },
  {
    menuId: -1,
    name: '待审核订单',
    key: 'pending_review_orders',
    operations: [
      { name: '全部通过', key: 'approve_all', method: 'TCP', functionCode: 11010, type: '操作' },
      {
        name: '通过勾选',
        key: 'approve_selected',
        method: 'TCP',
        functionCode: 11010,
        type: '操作',
      },
      { name: '全部驳回', key: 'reject_all', method: 'TCP', functionCode: 11010, type: '操作' },
      {
        name: '驳回勾选',
        key: 'reject_selected',
        method: 'TCP',
        functionCode: 11010,
        type: '操作',
      },
      { name: '通过', key: 'approve', method: 'TCP', functionCode: 11010, type: '操作' },
      { name: '驳回', key: 'reject', method: 'TCP', functionCode: 11010, type: '操作' },
    ],
  },
  {
    menuId: -1,
    name: '已审核订单',
    key: 'reviewed_orders',
    operations: [],
  },
  {
    menuId: -1,
    name: '已执行订单',
    key: 'executed_orders',
    operations: [],
  },
  {
    menuId: -1,
    name: '流程管理',
    key: 'process_management',
    operations: [
      { name: '创建', key: 'create', method: 'POST', url: '/workflow', type: '维护' },
      { name: '编辑', key: 'edit', method: 'PUT', url: '/workflow', type: '维护' },
      { name: '删除', key: 'delete', method: 'DELETE', url: '/workflow', type: '维护' },
    ],
  },
  {
    menuId: 100,
    name: '机构管理',
    key: 'organization_management',
    operations: [
      { name: '创建', key: 'create', method: 'POST', url: '/organization', type: '维护' },
      { name: '编辑', key: 'edit', method: 'PUT', url: '/organization', type: '维护' },
      { name: '删除', key: 'delete', method: 'DELETE', url: '/organization', type: '维护' },
      {
        name: '机构状态',
        key: 'enable_disable',
        method: 'PUT',
        url: '/organization',
        type: '维护',
      },
    ],
  },
  {
    menuId: 104,
    name: '角色管理',
    key: 'role_management',
    operations: [
      { name: '创建', key: 'create', method: 'POST', url: '/role', type: '维护' },
      { name: '编辑', key: 'edit', method: 'PUT', url: '/role', type: '维护' },
      { name: '删除', key: 'delete', method: 'DELETE', url: '/role', type: '维护' },
      { name: '克隆角色', key: 'clone_role', method: 'POST', url: '/role', type: '维护' },
      { name: '关联用户', key: 'associate_user', method: 'PUT', url: '/user', type: '维护' },
    ],
  },
  {
    menuId: 103,
    name: '用户管理',
    key: 'user_management',
    operations: [
      { name: '创建', key: 'create', method: 'POST', url: '/user', type: '维护' },
      { name: '编辑', key: 'edit', method: 'PUT', url: '/user', type: '维护' },
      { name: '删除', key: 'delete', method: 'DELETE', url: '/user', type: '维护' },
      { name: '用户状态', key: 'enable_disable', method: 'PUT', url: '/user', type: '维护' },
      { name: '强制下线', key: 'force_kick_out', method: 'PUT', url: '/user/logout', type: '维护' },
      {
        name: '重置密码',
        key: 'reset_password',
        method: 'PUT',
        url: '/user/password',
        type: '维护',
      },
      {
        name: '机器绑定',
        key: 'machine_binding',
        method: 'GET',
        url: '',
        type: '配置',
      },
      {
        name: '产品权限',
        key: 'product_permission',
        method: 'GET',
        url: '',
        type: '配置',
      },
      {
        name: '交易权限',
        key: 'trading_permission',
        method: 'GET',
        url: '',
        type: '配置',
      },
      {
        name: '操作日志',
        key: 'view_operation_log',
        method: 'GET',
        url: '',
        type: '配置',
      },
      {
        name: '登录日志',
        key: 'view_login_log',
        method: 'GET',
        url: '',
        type: '配置',
      },
    ],
  },
  {
    menuId: 106,
    name: '经纪商管理',
    key: 'broker_management',
    operations: [
      { name: '创建', key: 'create', method: 'POST', url: '/broker', type: '维护' },
      { name: '编辑', key: 'edit', method: 'PUT', url: '/broker', type: '维护' },
      { name: '删除', key: 'delete', method: 'DELETE', url: '/broker', type: '维护' },
    ],
  },
  {
    menuId: 107,
    name: '终端管理',
    key: 'terminal_management',
    operations: [
      { name: '创建', key: 'create', method: 'POST', url: '/terminal', type: '维护' },
      { name: '编辑', key: 'edit', method: 'PUT', url: '/terminal', type: '维护' },
      { name: '删除', key: 'delete', method: 'DELETE', url: '/terminal', type: '维护' },
      { name: '终端状态', key: 'enable_disable', method: 'PUT', url: '/terminal', type: '维护' },
    ],
  },
  {
    menuId: 200,
    name: '风控模版管理',
    key: 'risk_template_management',
    operations: [],
  },
  {
    menuId: 201,
    name: '风控消息',
    key: 'risk_control_message',
    operations: [],
  },
  {
    menuId: -1,
    name: '交易日',
    key: 'trading_day',
    operations: [],
  },
  {
    menuId: -1,
    name: '运维',
    key: 'operation_maintenance',
    operations: [],
  },
];

/**
 * 菜单及权限定义唯一性检查
 */
export function uniqueCheck() {
  const map = {} as any;
  for (let i = 0; i < PageOperations.length; i++) {
    const obj1 = PageOperations[i];
    const key1 = `${obj1.name}-${obj1.key}`;
    const matched1 = map[key1];

    if (matched1 !== undefined) {
      console.error('duplicated menu', `${obj1.name}-${obj1.key}`);
      return false;
    }

    for (let j = 0; j < obj1.operations.length; j++) {
      const obj2 = obj1.operations[j];
      const key2 = `${obj2.name}-${obj2.key}`;
      const matched2 = map[key2];

      if (matched2 !== undefined) {
        console.error('duplicated operation', `${obj1.name}-${obj1.key}-${obj2.name}-${obj2.key}`);
        return false;
      }
    }
  }
  return true;
}

/**
 * 从菜单操作权限生成SQL
 * @param defs
 */
export function makeSqls() {
  const insertions: string[] = [];
  const now = new Date().toISOString().replace('T', ' ').replace('Z', '');

  PageOperations.forEach(def => {
    def.operations.forEach(op => {
      const values = [
        0,
        `'${op.key}'`,
        `'${op.name}'`,
        op.functionCode || 0,
        op.url ? `'${op.url}'` : 'null',
        op.method ? `'${op.method}'` : 'null',
        `'${op.type}'`,
        def.menuId,
        0,
        `'${now}'`,
        `'${now}'`,
      ];
      insertions.push(`(${values.join()})`);
    });
  });

  const sql_insert =
    'insert into t_permission_copy1(userType, permissionName, permissionZhName, functionCode, url, method, type, menuId, defaultPermission, createTime, updateTime)';
  const sql_full = `${sql_insert} values \n${insertions.join(',\n')}`;
  console.log(sql_full);
}

function toUpperCamelCase(str: string) {
  if (!str || typeof str !== 'string') {
    return str;
  }

  return str
    .split(/_+/)
    .map(word => {
      if (word.length === 0) return '';
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
    .join('');
}

/**
 * 从菜单操作权限生成枚举定义
 * @param defs
 */
export function makeEnums(defs: MenuOperationDefinition[]) {
  const bigMap = {} as any;

  defs.forEach(def => {
    const smallMap = {} as any;
    def.operations.forEach(op => {
      smallMap[op.name] = op.key;
    });
    bigMap[def.key] = { name: def.name, subset: smallMap };
  });

  let result = '';
  for (const key in bigMap) {
    const { name, subset } = bigMap[key];
    result += `\n\n/**
        * ${name}
        */\n`;
    result += `export enum MenuPermit${toUpperCamelCase(key)} {`;
    result += Object.entries(subset)
      .map(([name, value]) => `${name} = '${value}',`)
      .join('\n');
    result += `}`;
  }

  console.log(result);
}

export default PageOperations;
