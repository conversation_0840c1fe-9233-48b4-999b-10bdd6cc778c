<script setup lang="ts">
import ChooseFundView from './ChooseFundView.vue';
import ChooseAccountView from './ChooseAccountView.vue';
import { reactive, useTemplateRef, watch } from 'vue';
import type { ProductInfo, AccountInfo } from '@/types';

const props = defineProps<{
  modelValue: boolean;
  title?: string;
  selectedFundIds: string[];
  selectedAccountIds: string[];
}>();

const dialogState = reactive({
  isVisible: false,
  targetFundIds: [] as string[],
  targetAccountIds: [] as string[],
});

watch(
  () => props.modelValue,
  newVal => {
    dialogState.isVisible = newVal;
    if (newVal) {
      dialogState.targetFundIds = [...props.selectedFundIds];
      dialogState.targetAccountIds = [...props.selectedAccountIds];
    }
  },
);

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  confirm: [selectedFunds: ProductInfo[], selectedAccounts: AccountInfo[]];
}>();

const hideDialog = () => {
  emit('update:modelValue', false);
  dialogState.targetFundIds = [];
  dialogState.targetAccountIds = [];
};

const $fundTable = useTemplateRef('$fundTable');
const $accountTable = useTemplateRef('$accountTable');

const confirmChoose = async () => {
  const funds = $fundTable.value?.getSelectedRows() || [];
  const accounts = $accountTable.value?.getSelectedRows() || [];
  emit('confirm', funds, accounts);
  hideDialog();
};
</script>

<template>
  <el-dialog
    :model-value="dialogState.isVisible"
    :title="title || '请选择产品'"
    width="900px"
    @close="hideDialog"
    draggable
  >
    <div h-350 of-y-hidden>
      <ChooseFundView
        ref="$fundTable"
        :ison="dialogState.isVisible"
        :selected-ids="dialogState.targetFundIds"
      />
    </div>
    <div h-350 of-y-hidden>
      <ChooseAccountView
        ref="$accountTable"
        :ison="dialogState.isVisible"
        :selected-ids="dialogState.targetAccountIds"
      />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="hideDialog">取消</el-button>
        <el-button type="primary" @click="confirmChoose">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
}
</style>
