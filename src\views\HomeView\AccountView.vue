<script setup lang="ts">
import AccountList from '@/components/AccountView/AccountList.vue';
import ComponentTabs from '@/components/common/ComponentTabs.vue';
import type { ComponentTab } from '@/types';
import { computed, ref } from 'vue';
import type { LegacyAccountInfo } from '../../../../xtrade-sdk/dist';

const activeAccount = ref<LegacyAccountInfo | null>(null);

const tabs = computed<ComponentTab[]>(() => [
  {
    label: '当日订单',
    component: 'TodayOrders',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '当日持仓',
    component: 'TodayPositions',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '当日成交',
    component: 'TodayRecords',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '历史订单',
    component: 'HistoryOrders',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '历史持仓',
    component: 'HistoryPositions',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '历史成交',
    component: 'HistoryRecords',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '历史权益',
    component: 'HistoryEquity',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '出入金',
    component: 'CashRecord',
    props: {
      activeItem: activeAccount.value,
    },
  },
]);

const handleRowClick = (row: LegacyAccountInfo) => {
  activeAccount.value = row;
};
</script>

<template>
  <div>
    <el-splitter layout="vertical">
      <el-splitter-panel>
        <AccountList h-full @row-click="handleRowClick" />
      </el-splitter-panel>
      <el-splitter-panel>
        <ComponentTabs :tabs h-full />
      </el-splitter-panel>
    </el-splitter>
  </div>
</template>

<style scoped></style>
