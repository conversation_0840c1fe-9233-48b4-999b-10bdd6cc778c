<template>
  <div class="form-container product-guide-slide" pr-10 overflow-y-auto overflow-x-hidden>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      class="typical-form"
      label-position="left"
      label-width="220px"
      w-full
      mt-10
    >
      <div class="half-col">
        <el-form-item label="产品名称" prop="fundName">
          <el-input v-model.trim="formData.fundName" placeholder="产品名称" clearable>
            <template #prefix>
              <i class="iconfont icon-consist"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="策略" prop="strategyType">
          <el-input v-model.trim="formData.strategyType" placeholder="策略类型">
            <template #prefix>
              <i class="iconfont icon-strategy"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="基准" prop="basisReference">
          <el-select
            v-model="formData.basisReference"
            placeholder="请选择基准"
            filterable
            clearable
          >
            <el-option
              v-for="(item, idx) in IndexBenchmarks"
              :key="idx"
              :label="item.label"
              :value="item.value"
            />
            <template #prefix>
              <i class="iconfont icon-riseup"></i>
            </template>
          </el-select>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="备案号" prop="amacCode">
          <el-input v-model.trim="formData.amacCode" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-document-code"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="成立日" prop="establishedDay">
          <el-date-picker
            v-model="formData.establishedDay"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYYMMDD"
          ></el-date-picker>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="基金经理" prop="fundManager">
          <el-input v-model.trim="formData.fundManager" placeholder="基金经理名字" clearable>
            <template #prefix>
              <i class="iconfont icon-user"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div class="half-col" pos-relative flex aic gap-10>
        <el-form-item label="基金类型" prop="fundType">
          <el-radio-group v-model="formData.fundType" class="typical-radio-group" w-full>
            <el-radio v-for="(item, idx) in filteredFundTypes" :key="idx" :label="item.value">
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="formData.fundType === FundTypeEnum.MOM子基金" mr-20>
          <el-form-item label="关联产品" prop="identity">
            <ProductSelect
              ref="productSelectRef"
              real
              from="product"
              v-model="formData.identity"
              :org-id="formData.orgId"
              :exclude-ids="contextProduct?.id ? [contextProduct.id] : []"
              @change="handleIdentityChange"
              @refresh="handleRefresh"
            />
          </el-form-item>
        </div>
      </div>
      <div class="half-col">
        <el-form-item label="产品状态" prop="closedFlag">
          <el-radio-group v-model="formData.closedFlag" class="typical-radio-group" w-full>
            <el-radio v-for="(item, idx) in ClosedFlags" :key="idx" :label="item.value">
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="管理机构" prop="orgId">
          <el-select
            v-model="formData.orgId"
            @change="handleOrgChange"
            placeholder="请选择管理机构"
            filterable
            clearable
          >
            <el-option
              v-for="(item, idx) in myOrgs"
              :key="idx"
              :label="item.orgName"
              :value="item.id"
            />
            <template #prefix>
              <i class="iconfont icon-building"></i>
            </template>
          </el-select>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" placeholder="备注信息" clearable>
            <template #prefix>
              <i class="iconfont icon-message"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="预警线" prop="alertNav">
          <el-input-number
            v-model="formData.alertNav"
            placeholder="预警线"
            :precision="4"
            :step="0.0001"
            :min="0"
            :controls="false"
          >
            <template #prefix>
              <i fs-18 class="iconfont icon-warning"></i>
            </template>
          </el-input-number>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="平仓线" prop="closeNav">
          <el-input-number
            v-model="formData.closeNav"
            placeholder="平仓线"
            :precision="4"
            :step="0.0001"
            :min="0"
            :controls="false"
          >
            <template #prefix>
              <i fs-18 class="iconfont icon-warning"></i>
            </template>
          </el-input-number>
        </el-form-item>
      </div>
    </el-form>
  </div>
  <div class="typical-dialog-footer" flex jcc gap-16 pt-16 pb-4>
    <el-button @click="cancel" w-200>取消</el-button>
    <el-button :disabled="loading" type="primary" @click="check" w-220>确定</el-button>
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  defineAsyncComponent,
  onMounted,
  ref,
  shallowRef,
  useTemplateRef,
  watch,
} from 'vue';
import { type ProductInfo } from '@/types';
import { getUser, hasGlobalDataPermission, isNone, deepClone } from '@/script';
import { Repos, type FormFundInfo, type MomOrganization } from '../../../../xtrade-sdk/dist';
import { ElMessage } from 'element-plus';
import { FundTypes, ClosedFlags, IndexBenchmarks, FundTypeEnum } from '@/enum/product';
const ProductSelect = defineAsyncComponent(() => import('../common/ProductSelect.vue'));

const { real, orgId } = defineProps<{
  /** 是否为真实产品, 为true时类型只能选择产品 */
  real?: boolean;
  /** 机构ID，用户回填管理机构字段 */
  orgId?: number;
}>();

const repoInstance = new Repos.AdminRepo();
const repoGovInstance = new Repos.GovernanceRepo();
const contextProduct = defineModel<ProductInfo | null>({});
const formRef = useTemplateRef('formRef');
const initForm = (): FormFundInfo => {
  return {
    fundName: '',
    strategyType: '',
    basisReference: '',
    amacCode: '',
    establishedDay: '',
    fundManager: '',
    fundType: FundTypeEnum.产品,
    identity: undefined,
    orgId: undefined,
    orgName: '',
    fundOrganization: '',
    closedFlag: false,
    remark: '',
    alertNav: undefined,
    closeNav: undefined,
  };
};
const formData = ref<FormFundInfo>(initForm());
const loading = ref(false);

const rules = {
  fundName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
  fundType: [{ required: true, message: '请选择基金类型', trigger: 'change' }],
  orgId: [{ required: true, message: '请选择管理机构', trigger: 'change' }],
  identity: [{ required: true, message: '请选择关联产品', trigger: 'change' }],
};

const currentUser = getUser()!;
const hasGlobalDataScope = ref(hasGlobalDataPermission());

const filteredFundTypes = computed(() => {
  return real ? FundTypes.filter(x => x.value === FundTypeEnum.产品) : FundTypes;
});

watch(
  () => contextProduct.value,
  newValue => {
    formData.value = newValue ? deepClone(newValue) : initForm();
  },
  { immediate: true },
);

const emitter = defineEmits<{
  cancel: [];
  save: [data: ProductInfo];
  refresh: [];
}>();

const cancel = () => {
  emitter('cancel');
  formRef.value?.clearValidate();
};

const check = () => {
  formRef.value?.validate(valid => {
    if (valid) {
      save();
    }
  });
};

function handleOrgChange() {
  const obj = formData.value;
  const matched = myOrgs.value.find(org => org.id === obj.orgId);
  obj.orgName = matched?.orgName || '';
  obj.fundOrganization = matched?.orgName || '';
}

async function save() {
  const isModify = !!contextProduct.value;
  const behavior = isModify ? '修改' : '创建';
  if (isNone(formData.value.orgName)) {
    handleOrgChange();
  }
  const row = deepClone(formData.value);

  // 补充必要的字段

  if (isNone(row.orgId)) {
    // 补充必要的字段
    const usr = getUser()!;
    row.orgId = usr.orgId;
    row.orgName = usr.orgName;
    row.fundOrganization = usr.orgName;
  }

  loading.value = true;
  const { errorCode, errorMsg, data } = isModify
    ? await repoGovInstance.UpdateProduct({
        ...contextProduct.value!,
        ...row,
      })
    : await repoGovInstance.CreateProduct(row);

  loading.value = false;
  if (errorCode === 0) {
    ElMessage.success(`${behavior}成功`);
    emitter('save', data!);
  } else {
    ElMessage.error(errorMsg || `${behavior}失败`);
  }
}

const orgs = shallowRef<MomOrganization[]>([]);
const myOrgs = computed(() => {
  return hasGlobalDataScope.value ? orgs.value : orgs.value.filter(x => x.id == currentUser.orgId);
});

async function requestOrgs() {
  orgs.value = (await repoInstance.QueryOrgs()).data || [];
}

function handleRefresh() {
  emitter('refresh');
}

function handleIdentityChange(value: string | undefined, product?: ProductInfo) {
  if (product) {
    formData.value.orgId = product.orgId;
    handleOrgChange();
  }
}

onMounted(async () => {
  await requestOrgs();
  if (orgId) {
    formData.value.orgId = orgId;
  }
  if (real) {
    formData.value.fundType = FundTypeEnum.产品;
  }
});

function clearValidate() {
  formRef.value?.clearValidate();
  formRef.value?.resetFields();
  console.log(formRef.value);
}

defineExpose({
  clearValidate,
});
</script>

<style scoped>
.form-container {
  .half-col {
    float: left;
    width: 50%;
  }
  :deep() {
    .el-form-item__label {
      position: relative;
    }
    .el-form-item {
      width: 95%;
      margin-right: 10px !important;
      .el-input-number {
        width: 100%;
        .el-input__wrapper {
          padding-left: 12px;
        }
      }
    }
  }
}
</style>
