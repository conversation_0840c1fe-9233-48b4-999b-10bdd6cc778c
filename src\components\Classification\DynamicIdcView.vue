<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import DynamicIdcDialog from './DynamicIdcDialog.vue';
import { onMounted, ref, useTemplateRef } from 'vue';
import { TableV2SortOrder, ElMessage } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { deepClone, formatDateTime, remove } from '@/script';
import { deleteConfirm } from '@/script/interaction';
import { Repos, type TradeDynamicIndicator } from '../../../../xtrade-sdk/dist';
import { DynamicConditionTypes, ExpressionTypes } from '@/enum/riskc';

interface CellRenderParam {
  rowData: TradeDynamicIndicator;
  cellData: any;
}

const repoInstance = new Repos.DynamicIndicatorRepo();

// 基础列定义
const columns: ColumnDefinition<TradeDynamicIndicator> = [
  {
    key: 'conditionType',
    title: '指标名称',
    width: 300,
    minWidth: 300,
    sortable: true,
    cellRenderer: renderCdtName,
  },
  // {
  //   key: 'expressionType',
  //   title: '运算符',
  //   width: 150,
  //   minWidth: 150,
  //   sortable: true,
  //   cellRenderer: renderExpressionType,
  // },
  // { key: 'value', title: '阈值', width: 200, minWidth: 200, sortable: true },
  { key: 'creatorUserName', title: '创建人', width: 200, minWidth: 200, sortable: true },
  { key: 'orgName', title: '机构', width: 200, minWidth: 200, sortable: true },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 200,
    minWidth: 200,
    sortable: true,
    cellRenderer: formatTs,
  },
];

// 行操作
const rowActions: RowAction<TradeDynamicIndicator>[] = [
  {
    label: '编辑',
    icon: 'edit',
    onClick: row => {
      editRow(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    onClick: row => {
      deleteRow(row);
    },
  },
];

/**
 * 生成动态条件描述
 */
function renderConditionDesc(row: TradeDynamicIndicator) {
  const { conditionType, expressionType, value } = row;
  const matched = DynamicConditionTypes.find(x => x.value == conditionType)!;
  const matched2 = ExpressionTypes.find(x => x.value == expressionType)!;
  return matched.fmt.replace('{0}', matched2.label).replace('{1}', value.toString());
}

function renderCdtName(params: CellRenderParam) {
  return <span>{renderConditionDesc(params.rowData)}</span>;
}

function formatTs(params: CellRenderParam) {
  return <span>{formatDateTime(params.cellData)}</span>;
}

// function renderExpressionType(params: CellRenderParam) {
//   return <span>{renderLabel(params.rowData.expressionType, ExpressionTypes)}</span>;
// }

const records = ref<TradeDynamicIndicator[]>([]);
const tableRef = useTemplateRef('tableRef');

// 对话框相关
const dialogVisible = ref(false);
const editingRow = ref<TradeDynamicIndicator | undefined>();

// 新建分类
const handleCreate = () => {
  editingRow.value = undefined;
  dialogVisible.value = true;
};

// 编辑分类
function editRow(row: TradeDynamicIndicator) {
  editingRow.value = deepClone(row);
  dialogVisible.value = true;
}

// 删除分类
async function deleteRow(row: TradeDynamicIndicator) {
  const result = await deleteConfirm('删除确认', `是否删除该指标： ${renderConditionDesc(row)}？`);
  if (result !== true) {
    return;
  }

  const resp = await repoInstance.deleteDynamicIndicator(row.id);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success('已删除');
    const list = records.value;
    remove(list, x => x.id == row.id);
  } else {
    ElMessage.error(`删除失败：${errorCode}/${errorMsg}`);
  }
}

// 对话框成功回调
const handleDialogSuccess = async () => {
  await request();
};

async function request() {
  const list = (await repoInstance.getDynamicIndicators()).data || [];
  records.value = list;
}

onMounted(() => {
  request();
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'id', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="records"
    :row-actions="rowActions"
    :row-action-width="150"
    select
    fixed
    show-index
  >
    <template #actions>
      <div class="actions" flex aic>
        <el-button type="primary" @click="handleCreate">
          <i class="iconfont icon-add-new" mr-5></i>
          <span>新建动态指标</span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>

  <!-- 动态指标编辑对话框 -->
  <DynamicIdcDialog
    v-model="dialogVisible"
    :dynamicIndicator="editingRow"
    @success="handleDialogSuccess"
  />
</template>

<style scoped></style>
