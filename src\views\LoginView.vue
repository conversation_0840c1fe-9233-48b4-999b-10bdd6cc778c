<script setup lang="ts">
import { ref, shallowRef, useTemplateRef } from 'vue';
import { ElMessage, ElForm } from 'element-plus';
import { LoginService } from '@/api';
import { getServer, setServer, setUser, try2ConfigServer } from '@/script';
import router from '@/router';
import type { ServerConfig } from '@/types';
import { GlobalState } from '../../../xtrade-sdk';
import { getConfigedServers, getMacAddress, getOsInfo } from '../../shared/servers';

const macInfo = getMacAddress();
const osInfo = getOsInfo();

const rules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  server: [{ required: true, message: '请选择服务器', trigger: 'change' }],
};

const loading = ref(false);

const form = ref({
  username: 'admin',
  password: 'gaoyu_8888',
  server: '',
});

const formRef = useTemplateRef('formRef');
const servers = shallowRef<ServerConfig[]>(getConfigedServers());

function initServers() {
  const lastServer = getServer();
  const matched = servers.value.find(item => item.name === lastServer?.name);

  if (matched) {
    form.value.server = matched.name;
  } else {
    const first = servers.value[0];
    form.value.server = first.name;
    setServer(JSON.stringify(first));
  }
}

initServers();

function handleServerChange(value: string) {
  const matched = servers.value.find(item => item.name == value);
  if (matched) {
    setServer(JSON.stringify(matched));
    try2ConfigServer();
  }
}

const login = () => {
  if (!formRef.value) return;
  formRef.value.validate(async valid => {
    if (valid) {
      try {
        loading.value = true;
        const resp = await LoginService.login(
          form.value.username,
          form.value.password,
          macInfo,
          osInfo,
          false,
        )!;
        const { trade, quote } = resp;
        const asok = trade && trade.errorCode == 0;

        if (asok) {
          setUser(GlobalState.GetUserInfo()!);
          router.push({ name: 'home' });
        } else {
          ElMessage.error(trade?.errorMsg || quote?.errorMsg || '登录失败');
        }
      } finally {
        loading.value = false;
      }
    }
  });
};
</script>

<template>
  <div class="login-view" flex justify-around aic>
    <div class="intro" h-300 w-400>
      <div c-white fs-60 fw-600 mb-40>交易极速柜台</div>
      <div c-white fs-50 fw-200>感受极致体验</div>
    </div>
    <div class="login-box" p-24 b-rd-20 w-450>
      <div c-white fs-40 fw-600 mb-28>欢迎回来</div>
      <el-form w-full ref="formRef" :model="form" :rules="rules" label-position="top">
        <el-form-item label="用户名" prop="username">
          <el-input placeholder="请输入用户名" v-model="form.username" clearable />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.password" type="password" show-password clearable />
        </el-form-item>
        <el-form-item label="服务器" prop="server">
          <el-select
            popper-class="login-server-popper"
            :show-arrow="false"
            placeholder="请选择服务器"
            v-model="form.server"
            @change="handleServerChange"
          >
            <el-option
              v-for="(server, idx) in servers"
              :key="idx"
              :value="server.name"
              :label="server.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            class="login-button"
            w-full
            mt-6
            color="var(--g-block-bg-14)"
            :disabled="loading"
            @click="login"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<style scoped>
.login-view {
  background: url(../assets/image/login_background.png) no-repeat center center;
  background-size: cover;
  .login-box {
    backdrop-filter: blur(42px);
    background-color: rgba(255, 255, 255, 0.1);
    .el-form {
      :deep(.el-input) {
        --el-input-height: 48px;
        --el-input-border-color: var(--g-border-color-1);
        --el-border-color-hover: white;
        --el-input-focus-border-color: var(--g-block-bg-13);
        .el-input__wrapper {
          background-color: transparent;
        }
      }
      :deep(.el-select) {
        --el-border-color: var(--g-border-color-1);
        --el-border-color-hover: white;
        --el-color-primary: var(--g-block-bg-4);
        .el-select__wrapper {
          background-color: transparent;
          min-height: 48px;
        }
      }
    }

    .login-button {
      height: 48px;
    }
  }
}
</style>
