import type { AnyObject } from '@/types';
import { renderLabel } from '@/script';
import { IdcComponentNameDef } from './ComponentNameDef';

import {
  AlertTypes,
  ExpressionTypes,
  FuturesMarginRatioTypes,
  MarketValueRatioTypes,
  NavValueStopLossTypes,
  PriceDeviationTypes,
  RiskBsFlags,
  RiskPriceTypes,
  RiskStatisticsTypes,
} from '@/enum/riskc';

type MethodSignatureType = {
  [componentName: string]: (riskParams: AnyObject) => string | string[];
};

export const RiskSummarizationMethod: MethodSignatureType = {
  [IdcComponentNameDef.BlackList]: function (riskParams: AnyObject) {
    const { alertType } = riskParams;
    return `遇到黑名单合约时，${renderLabel(alertType, AlertTypes)}`;
  },

  [IdcComponentNameDef.WhiteList]: function (riskParams: AnyObject) {
    const { alertType } = riskParams;
    return `不在白名单合约时，${renderLabel(alertType, AlertTypes)}`;
  },

  [IdcComponentNameDef.SingleOrderMaxVolume]: function (riskParams: AnyObject) {
    const { bsFlag, paramAlert, paramBlock } = riskParams;
    return [
      `「${renderLabel(bsFlag, RiskBsFlags)}」单笔委托量：`,
      `「${renderLabel(paramAlert.expression, ExpressionTypes)}${paramAlert.value}万(股/份/张/手)时」${renderLabel(paramAlert.alertType, AlertTypes)};`,
      `「${renderLabel(paramBlock.expression, ExpressionTypes)}${paramBlock.value}万(股/份/张/手)时」${renderLabel(paramBlock.alertType, AlertTypes)}；`,
    ];
  },

  [IdcComponentNameDef.AccountOrderMaxVolume]: function (riskParams: AnyObject) {
    const { bsFlag, paramAlert, paramBlock } = riskParams;
    return [
      `「${renderLabel(bsFlag, RiskBsFlags)}」单笔委托量：`,
      `「${renderLabel(paramAlert.expression, ExpressionTypes)}${paramAlert.value}万(股/份/张/手)时」${renderLabel(paramAlert.alertType, AlertTypes)};`,
      `「${renderLabel(paramBlock.expression, ExpressionTypes)}${paramBlock.value}万(股/份/张/手)时」${renderLabel(paramBlock.alertType, AlertTypes)}；`,
    ];
  },

  [IdcComponentNameDef.SingleOrderMaxAmount]: function (riskParams: AnyObject) {
    const { bsFlag, paramAlert, paramBlock } = riskParams;
    return [
      `「${renderLabel(bsFlag, RiskBsFlags)}」单笔最大交易额：`,
      `「${renderLabel(paramAlert.expression, ExpressionTypes)}${paramAlert.value}万元时」${renderLabel(paramAlert.alertType, AlertTypes)};`,
      `「${renderLabel(paramBlock.expression, ExpressionTypes)}${paramBlock.value}万元时」${renderLabel(paramBlock.alertType, AlertTypes)}；`,
    ];
  },

  [IdcComponentNameDef.NetBuyAmount]: function (riskParams: AnyObject) {
    const { paramAlert, paramBlock } = riskParams;
    return [
      `「买入」各类资产时，净买入金额「${renderLabel(paramAlert.expression, ExpressionTypes)}${paramAlert.value}万元时」`,
      `${renderLabel(paramAlert.alertType, AlertTypes)}，「${renderLabel(paramBlock.expression, ExpressionTypes)}${paramBlock.value}万元时」`,
      `${renderLabel(paramBlock.alertType, AlertTypes)}；`,
    ];
  },

  [IdcComponentNameDef.TradeFrequency]: function (riskParams: AnyObject) {
    const { paramAlert, paramBlock } = riskParams;
    return [
      `流量控制，「${renderLabel(paramAlert.expression, ExpressionTypes)}${paramAlert.value}笔 / 每${paramAlert.second}秒时`,
      `${renderLabel(paramAlert.alertType, AlertTypes)}」；`,
      `「${renderLabel(paramBlock.expression, ExpressionTypes)}${paramBlock.value}笔 / 每${paramBlock.second}秒时`,
      `${renderLabel(paramBlock.alertType, AlertTypes)}」；`,
    ];
  },

  [IdcComponentNameDef.OrderCancelRate]: function (riskParams: AnyObject) {
    const { expression, orderCount, paramAlert, paramForbidCancel } = riskParams;
    return [
      `「委托次数」「${renderLabel(expression, ExpressionTypes)}${orderCount}次时」，股票撤单比率按汇总限制；`,
      `「${renderLabel(paramAlert.expression, ExpressionTypes)}${paramAlert.value}%时」${renderLabel(paramAlert.alertType, AlertTypes)};`,
      `「${renderLabel(paramForbidCancel.expression, ExpressionTypes)}${paramForbidCancel.value}%时」${renderLabel(paramForbidCancel.alertType, AlertTypes)}；`,
    ];
  },

  [IdcComponentNameDef.InvalidOrderRate]: function (riskParams: AnyObject) {
    const { expression, orderCount, paramAlert, paramForbidOrder } = riskParams;
    return [
      `「委托次数」「${renderLabel(expression, ExpressionTypes)}${orderCount}次时」，废单比率按汇总限制；`,
      `「${renderLabel(paramAlert.expression, ExpressionTypes)}${paramAlert.value}%时」${renderLabel(paramAlert.alertType, AlertTypes)};`,
      `「${renderLabel(paramForbidOrder.expression, ExpressionTypes)}${paramForbidOrder.value}%时」${renderLabel(paramForbidOrder.alertType, AlertTypes)}；`,
    ];
  },

  [IdcComponentNameDef.IndayReversedDirection]: function (riskParams: AnyObject) {
    const { intraDayReversalAlert } = riskParams;
    return `日内反向「${renderLabel(intraDayReversalAlert, AlertTypes)}」；`;
  },

  [IdcComponentNameDef.SelfTrade]: function (riskParams: AnyObject) {
    const { selfTradeAlert } = riskParams;
    return `自成交「${renderLabel(selfTradeAlert, AlertTypes)}」；`;
  },

  [IdcComponentNameDef.PriceDeviation]: function (riskParams: AnyObject) {
    const { deviationType, paramAlert, paramBlock } = riskParams;
    return [
      `${renderLabel(deviationType, PriceDeviationTypes)}`,
      `「${renderLabel(paramAlert.expression, ExpressionTypes)}${paramAlert.value}万元时」${renderLabel(paramAlert.alertType, AlertTypes)}，`,
      `「${renderLabel(paramBlock.expression, ExpressionTypes)}${paramBlock.value}万元时」${renderLabel(paramBlock.alertType, AlertTypes)}；`,
    ];
  },

  [IdcComponentNameDef.PriceLimit]: function (riskParams: AnyObject) {
    const { marketPrice, upPrice, downPrice } = riskParams;
    const values = [marketPrice, upPrice, downPrice].filter(x => x > 0);
    const matches = RiskPriceTypes.filter(x => values.includes(x.value));
    return matches.map(x => x.label).join('、');
  },

  [IdcComponentNameDef.MarketValue]: function (riskParams: AnyObject) {
    const { riskStatisticsType, marketValueType, paramAlert, paramBlock } = riskParams;
    return [
      `按「${renderLabel(riskStatisticsType, RiskStatisticsTypes)}」与「${renderLabel(marketValueType, MarketValueRatioTypes)}」统计时，`,
      `当净值「${renderLabel(paramAlert.navExpression, ExpressionTypes)}${paramAlert.nav}」时，`,
      `该指标「${renderLabel(paramAlert.expression, ExpressionTypes)}${paramAlert.value}万元」时，${renderLabel(paramAlert.alertType, AlertTypes)}」；`,
      `当净值「${renderLabel(paramBlock.navExpression, ExpressionTypes)}${paramBlock.nav}」时，`,
      `该指标「${renderLabel(paramBlock.expression, ExpressionTypes)}${paramBlock.value}万元」时，${renderLabel(paramBlock.alertType, AlertTypes)}」；`,
    ];
  },

  [IdcComponentNameDef.MarketValueRatio]: function (riskParams: AnyObject) {
    const { riskStatisticsType, marketValueType, paramAlert, paramBlock } = riskParams;
    return [
      `按「${renderLabel(riskStatisticsType, RiskStatisticsTypes)}」与「${renderLabel(marketValueType, MarketValueRatioTypes)}」统计时，`,
      `当净值「${renderLabel(paramAlert.navExpression, ExpressionTypes)}${paramAlert.nav}」时，`,
      `该指标「${renderLabel(paramAlert.expression, ExpressionTypes)}${paramAlert.value}万元」时，${renderLabel(paramAlert.alertType, AlertTypes)}」；`,
      `当净值「${renderLabel(paramBlock.navExpression, ExpressionTypes)}${paramBlock.nav}」时，`,
      `该指标「${renderLabel(paramBlock.expression, ExpressionTypes)}${paramBlock.value}万元」时，${renderLabel(paramBlock.alertType, AlertTypes)}」；`,
    ];
  },

  [IdcComponentNameDef.MarketCapitalRatio]: function (riskParams: AnyObject) {
    const { riskStatisticsType, marketValueType, paramAlert, paramBlock } = riskParams;
    return [
      `按「${renderLabel(riskStatisticsType, RiskStatisticsTypes)}」与「${renderLabel(marketValueType, MarketValueRatioTypes)}」统计时，`,
      `当净值「${renderLabel(paramAlert.navExpression, ExpressionTypes)}${paramAlert.nav}」时，`,
      `该指标「${renderLabel(paramAlert.expression, ExpressionTypes)}${paramAlert.value}万元」时，${renderLabel(paramAlert.alertType, AlertTypes)}」；`,
      `当净值「${renderLabel(paramBlock.navExpression, ExpressionTypes)}${paramBlock.nav}」时，`,
      `该指标「${renderLabel(paramBlock.expression, ExpressionTypes)}${paramBlock.value}万元」时，${renderLabel(paramBlock.alertType, AlertTypes)}」；`,
    ];
  },

  [IdcComponentNameDef.FlowableMarketCapitalRatio]: function (riskParams: AnyObject) {
    const { riskStatisticsType, marketValueType, paramAlert, paramBlock } = riskParams;
    return [
      `按「${renderLabel(riskStatisticsType, RiskStatisticsTypes)}」与「${renderLabel(marketValueType, MarketValueRatioTypes)}」统计时，`,
      `当净值「${renderLabel(paramAlert.navExpression, ExpressionTypes)}${paramAlert.nav}」时，`,
      `该指标「${renderLabel(paramAlert.expression, ExpressionTypes)}${paramAlert.value}万元」时，${renderLabel(paramAlert.alertType, AlertTypes)}」；`,
      `当净值「${renderLabel(paramBlock.navExpression, ExpressionTypes)}${paramBlock.nav}」时，`,
      `该指标「${renderLabel(paramBlock.expression, ExpressionTypes)}${paramBlock.value}万元」时，${renderLabel(paramBlock.alertType, AlertTypes)}」；`,
    ];
  },

  [IdcComponentNameDef.NavStopLoss]: function (riskParams: AnyObject) {
    const { expression, netValueStopLossType, value, alertType } = riskParams;
    return [
      `当净值「${renderLabel(expression, ExpressionTypes)}`,
      `${renderLabel(netValueStopLossType, NavValueStopLossTypes)}${value}」时，`,
      `${renderLabel(alertType, AlertTypes)}`,
    ];
  },

  [IdcComponentNameDef.FuturesPositionRatio]: function (riskParams: AnyObject) {
    const { paramAlert, paramBlock } = riskParams;
    return [
      `当净值「${renderLabel(paramAlert.navExpression, ExpressionTypes)}${paramAlert.nav}」时，`,
      `该指标「${renderLabel(paramAlert.expression, ExpressionTypes)}${paramAlert.value}万元」时，${renderLabel(paramAlert.alertType, AlertTypes)}」；`,
      `当净值「${renderLabel(paramBlock.navExpression, ExpressionTypes)}${paramBlock.nav}」时，`,
      `该指标「${renderLabel(paramBlock.expression, ExpressionTypes)}${paramBlock.value}万元」时，${renderLabel(paramBlock.alertType, AlertTypes)}」；`,
    ];
  },

  [IdcComponentNameDef.FuturesMargin]: function (riskParams: AnyObject) {
    const { riskStatisticsType, marketValueType, paramAlert, paramBlock } = riskParams;
    return [
      `按「${renderLabel(riskStatisticsType, RiskStatisticsTypes)}」与「${renderLabel(marketValueType, FuturesMarginRatioTypes)}」统计时，`,
      `当净值「${renderLabel(paramAlert.navExpression, ExpressionTypes)}${paramAlert.nav}」时，`,
      `该指标「${renderLabel(paramAlert.expression, ExpressionTypes)}${paramAlert.value}万元」时，${renderLabel(paramAlert.alertType, AlertTypes)}」；`,
      `当净值「${renderLabel(paramBlock.navExpression, ExpressionTypes)}${paramBlock.nav}」时，`,
      `该指标「${renderLabel(paramBlock.expression, ExpressionTypes)}${paramBlock.value}万元」时，${renderLabel(paramBlock.alertType, AlertTypes)}」；`,
    ];
  },
};
