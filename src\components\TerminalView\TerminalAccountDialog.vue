<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import { shallowRef, computed, watch } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import type { ColumnDefinition } from '@/types';
import { AdminService } from '@/api';
import { formatDateTime, renderLabel } from '@/script';
import { AssetType, type LegacyAccountInfo, type MomTerminal } from '../../../../xtrade-sdk/dist';

const { terminal } = defineProps<{
  terminal?: MomTerminal;
}>();

const visible = defineModel<boolean>();

const AssetTypes = Object.values(AssetType);

// 列定义
const columns: ColumnDefinition<LegacyAccountInfo> = [
  {
    key: 'assetType',
    title: '账号类型',
    width: 120,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      return <span>{renderLabel(cellData, AssetTypes)}</span>;
    },
  },
  {
    key: 'accountName',
    title: '账号',
    width: 150,
    sortable: true,
  },
  {
    key: 'brokerName',
    title: '券商名称',
    width: 150,
    sortable: true,
  },
  {
    key: 'connectionStatus',
    title: '状态',
    width: 100,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      return (
        <span class={cellData ? 'c-[var(--g-green)]' : 'c-[var(--g-red)]'}>
          {cellData ? '在线' : '离线'}
        </span>
      );
    },
  },
  {
    key: 'id',
    title: '连接成功时间(无字段)',
    width: 180,
    sortable: true,
    cellRenderer: ({ rowData }) => {
      return <span>--</span>;
    },
  },
];

const records = shallowRef<LegacyAccountInfo[]>([]);

// 对话框标题
const dialogTitle = computed(() => {
  return terminal ? `${terminal.terminalName} - 账号列表` : '终端账号列表';
});

// 监听visible变化，加载数据
watch(visible, async val => {
  if (val && terminal) {
    await loadTerminalAccounts();
  }
});

// 加载终端账号数据
async function loadTerminalAccounts() {
  if (!terminal) return;

  const data = await AdminService.getTerminalAccounts(terminal.id);
  console.log(111, data);

  records.value = data;
}

// 关闭对话框
function handleClose() {
  visible.value = false;
}
</script>

<template>
  <el-dialog
    destroy-on-close
    draggable
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    @close="handleClose"
  >
    <div h-500>
      <VirtualizedTable
        :sort="{ key: 'connectTime', order: TableV2SortOrder.DESC }"
        :columns="columns"
        :data="records"
        fixed
      />
    </div>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
