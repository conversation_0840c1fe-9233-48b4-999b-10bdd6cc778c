/**
 * TICK行情类型
 */
export enum TickType {
  /** 简单TICK */
  simple = 4,
  /** 标准TICK */
  tick = 5,
  /** K线TICK */
  kline = 20,
  /** 成交TICK */
  transaction = 60,
  /** 订单队列TICK */
  queue = 70,
  /** 预埋单TICK */
  preplaced = 71,
  /** 前序排单TICK */
  front = 72,
  /** 前序撤单TICK */
  frontCancel = 73,
}

/**
 * 主体类型
 */
export const IdentityType = {
  Account: { label: "帐号", value: 1 },
  Strategy: { label: "策略", value: 2 },
  Fund: { label: "产品", value: 3 },
  Report: { label: "报告", value: 4 },
  Application: { label: "应用", value: 5 },
  Basket: { label: "交易篮子", value: 6 },
  Order: { label: "订单", value: 7 },
  FundAccount: { label: "产品账号", value: 8 },
  StrategyAccount: { label: "策略账号", value: 9 },
  FundGroup: { label: "产品组", value: 10 },
};

/**
 * 主体类型
 */
export const IdentityTypes = Object.values(IdentityType);