<script setup lang="ts">
import { ref, computed, watch, shallowRef, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { HedgeFlagEnum, OrderPriceTypeEnum, TradeDirectionEnum, PositionEffectEnum } from '@/enum';
import type {
  TradeChannel,
  InstrumentInfo,
  TradeButton,
  StandardTick,
  FinanceAccountDetail,
} from '@/types';
import InstrumentInput from '@/components/common/InstrumentInput.vue';
import { formatNumber, getUser, isValidNumber } from '@/script';
import { TradingService } from '@/api';
import {
  BusinessFlagEnum,
  type PositionInfo,
  type RegularOrder,
} from '../../../../../xtrade-sdk/dist';

const { direction, activeChannel, activeAccount, lastTick } = defineProps<{
  direction: TradeDirectionEnum;
  activeChannel: TradeChannel;
  lastTick?: StandardTick;
  activeAccount?: FinanceAccountDetail;
}>();

/** 比例选项数组 */
const ratioOptions = [
  { label: '1/2', value: 0.5 },
  { label: '1/3', value: 1 / 3 },
  { label: '1/4', value: 0.25 },
  { label: '全部', value: 1 },
];

/** 普通买入 */
const buyBtn: TradeButton = {
  label: '买入',
  businessFlag: BusinessFlagEnum.普通买卖,
};

/** 普通卖出 */
const sellBtn: TradeButton = {
  label: '卖出',
  businessFlag: BusinessFlagEnum.普通买卖,
};

/** 选中的合约 */
const selectedInstrument = ref<InstrumentInfo>();

/** 仅在合约变化时更新价格 */
const updatePriceFlag = ref(false);

/** 价格输入 */
const price = ref<number>(0);

/** 数量输入 */
const volume = ref<number>(100);

/** 当前交易按钮 */
const currentTradeButton = shallowRef<TradeButton>(buyBtn);

/** 最新价格 */
const lastPrice = computed(() => {
  if (!lastTick) return 0;
  return lastTick.lastPrice;
});

/** 下单按钮背景色 */
const tradeBgColor = computed(() => {
  return direction > 0 ? 'var(--g-bg-red)' : 'var(--g-bg-green)';
});

/** 下单按钮悬浮颜色 */
const tradeHoverColor = computed(() => {
  return direction > 0 ? 'var(--g-bg-red-hover)' : 'var(--g-bg-green-hover)';
});

/** 计算买卖上下限价格 */
const buyUpperLimit = computed(() => {
  if (!lastTick) return '';

  // 最新价格的102%，不超过涨停价
  const price = lastPrice.value * 1.02;
  return Math.min(price, lastTick.upperLimitPrice);
});

/** 计算卖出下限价格 */
const sellLowerLimit = computed(() => {
  if (!lastTick) return '';

  // 最新价格的98%，不小于跌停价
  const price = lastPrice.value * 0.98;
  return Math.max(price, lastTick.lowerLimitPrice);
});

/** 价格限制信息数组 */
const priceInfos = computed(() => [
  {
    label: '卖下限',
    value: sellLowerLimit.value,
    color: 'c-[--g-green]',
    onClick: () => clickPrice(sellLowerLimit.value),
  },
  {
    label: '买上限',
    value: buyUpperLimit.value,
    color: 'c-[--g-red]',
    onClick: () => clickPrice(buyUpperLimit.value),
  },
  {
    label: '跌停',
    value: lastTick?.lowerLimitPrice,
    color: 'c-[--g-green]',
    onClick: () => clickPrice(lastTick?.lowerLimitPrice),
  },
  {
    label: '涨停',
    value: lastTick?.upperLimitPrice,
    color: 'c-[--g-red]',
    onClick: () => clickPrice(lastTick?.upperLimitPrice),
  },
]);

/** 预估金额 */
const estimateCost = computed(() => {
  if (!selectedInstrument.value) return '';

  // 计算预估金额
  const amount = price.value * volume.value;
  return formatNumber(amount, {
    abbreviate: true,
  });
});

/** 是否显示预估金额 */
const visible = computed(() => {
  if (!price.value) return false;
  if (!volume.value) return false;
  return !!estimateCost.value;
});

/** 买入按钮配置 */
const buyButtons = computed<TradeButton[]>(() => {
  if (!activeChannel.credit) {
    return [buyBtn];
  }
  return [
    buyBtn,
    {
      label: '融资买入',
      businessFlag: BusinessFlagEnum.融资融券,
    },
    {
      label: '买券还券',
      businessFlag: BusinessFlagEnum.还款还券,
    },
    {
      label: '优先担保品买',
    },
    {
      label: '优先融资买',
    },
  ];
});

/** 卖出按钮配置 */
const sellButtons = computed<TradeButton[]>(() => {
  if (!activeChannel.credit) {
    return [sellBtn];
  }
  return [
    sellBtn,
    {
      label: '融券卖出',
      handler: () => {},
      businessFlag: BusinessFlagEnum.融资融券,
    },
    {
      label: '卖券还款',
      handler: () => {},
      businessFlag: BusinessFlagEnum.还款还券,
    },
  ];
});

/** 当前交易方向按钮组 */
const tradeButtons = computed(() => {
  return direction === TradeDirectionEnum.买入 ? buyButtons.value : sellButtons.value;
});

/** 监听合约变化，更新价格 */
watch(
  () => selectedInstrument.value?.instrument,
  newVal => {
    if (newVal) {
      // 如果选择了新合约，则在最新行情到达后设置最新价格
      updatePriceFlag.value = true;
    } else {
      nextTick(async () => {
        price.value = 0;
      });
    }
  },
);

/** 监听最新行情，更新价格 */
watch(lastPrice, () => {
  if (updatePriceFlag.value && lastTick) {
    updatePriceFlag.value = false;
    price.value = lastPrice.value;
  }
});

/** 监听交易渠道，更新默认交易按钮 */
watch(
  () => activeChannel,
  () => {
    currentTradeButton.value = direction === TradeDirectionEnum.买入 ? buyBtn : sellBtn;
  },
);

/** 监听交易方向，更新默认交易按钮 */
watch(
  () => direction,
  newDirection => {
    currentTradeButton.value = newDirection === TradeDirectionEnum.买入 ? buyBtn : sellBtn;
  },
);

/** 通过仓位设置数量 */
const setVolumeByPositionPercent = async (ratio: number) => {
  if (!selectedInstrument.value || !activeAccount || price.value <= 0) {
    return;
  }

  if (direction === TradeDirectionEnum.买入) {
    // 买入逻辑：根据可用资金计算
    const totalAvailable = activeAccount.available || 0; // 账户可用资金
    const taxRate = 0.001; // 0.1% 税费
    const actualAvailable = totalAvailable * (1 - taxRate); // 实际可用资金（扣除税费）
    const expectedAmount = totalAvailable * ratio; // 期望下单金额
    const actualAmount = Math.min(actualAvailable, expectedAmount); // 取较小值

    // 计算可买数量并向下取整为100的倍数
    const calculatedVolume = Math.floor(actualAmount / price.value / 100) * 100;

    // 确保最小交易量为100
    volume.value = calculatedVolume < 100 ? 100 : calculatedVolume;
  } else {
    // 卖出逻辑：根据持仓数量计算
    // 获取当前合约的可用持仓
    const availablePosition = await getMatchedPosition();

    volume.value =
      availablePosition <= 100
        ? availablePosition
        : Math.floor((availablePosition * ratio) / 100) * 100;
  }
};

/** 获取当前合约的可用持仓 */
const getMatchedPosition = async () => {
  if (!activeAccount) {
    return 0;
  }

  // 从持仓列表中找到匹配的持仓
  // TODO: 这里应该调用API获取持仓数据
  const positions: PositionInfo[] = [];
  const matchedPosition = positions.find(
    x => x.instrument === selectedInstrument.value?.instrument,
  );

  if (matchedPosition) {
    // TODO: 确定持仓信息合约可用字段
    return matchedPosition.todayPosition;
  } else {
    return 0;
  }
};

/** 点击价格限制信息设置价格 */
const clickPrice = (val: number | unknown) => {
  if (isValidNumber(val)) {
    price.value = val;
  } else {
    price.value = 0;
  }
};

/** 格式化价格，保留两位小数 */
const formatPrice = (price: number | unknown): string => {
  return formatNumber(price, { fix: 2, default: '--' });
};

/** 选择交易类型 */
const handleSelectTradeType = (button: TradeButton) => {
  currentTradeButton.value = button;
};

/** 下单操作 */
const handleSubmitOrder = async () => {
  if (!activeAccount) {
    ElMessage.warning('请选择账户');
    return;
  }

  if (!selectedInstrument.value) {
    ElMessage.warning('请选择合约');
    return;
  }

  if (!price.value) {
    ElMessage.warning('请输入有效价格');
    return;
  }

  if (!volume.value) {
    ElMessage.warning('请输入有效数量');
    return;
  }

  // 检查下单金额
  if (direction === TradeDirectionEnum.买入) {
    const totalAmount = price.value * volume.value;
    const availableAmount = activeAccount!.available || 0;
    // const creditAvailable = activeAccount!.enableCreditBuy || 0; // todo25
    const creditAvailable = 0;

    let totalAvailable = 0;
    if (currentTradeButton.value.label.includes('优先')) {
      totalAvailable = creditAvailable + availableAmount;
    } else if (currentTradeButton.value.label == '融资买入') {
      totalAvailable = creditAvailable;
    } else {
      totalAvailable = availableAmount;
    }

    // 扣除税费
    totalAvailable *= 0.999;

    if (totalAmount > totalAvailable) {
      ElMessage.warning('可用资金不足');
      return;
    }
  }

  try {
    await ElMessageBox.confirm(
      `
      <div>合约: ${selectedInstrument.value.instrumentName}</div>
      <div>价格: ${price.value}</div>
      <div>数量: ${volume.value}</div>
      <div>方向: ${currentTradeButton.value.label}</div>
      `,
      '确认下单',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        type: 'warning',
      },
    );

    // 用户确认后执行下单
    console.log('Submit order:', {
      instrument: selectedInstrument.value.instrument,
      instrumentName: selectedInstrument.value.instrumentName,
      price: price.value,
      volume: volume.value,
      direction: direction,
    });

    const orders = buildOrders();
    await submitOrders(orders);
  } catch {
    // 用户取消下单
  }
};

/** 构建订单信息 */
const buildOrders = () => {
  const orders: RegularOrder[] = [];

  /** 通用创建订单 */
  const createOrder = (businessFlag: BusinessFlagEnum, vol?: number): RegularOrder => {
    return {
      accountId: activeAccount!.id,
      userId: getUser()!.userId,
      instrument: selectedInstrument.value?.instrument || '',
      price: price.value,
      priceType: OrderPriceTypeEnum.限价,
      bsFlag: direction,
      positionEffect: PositionEffectEnum.开仓,
      hedgeFlag: HedgeFlagEnum.投机,
      orderTime: Date.now(),
      customId: `new-xtrade-mannual-${Date.now()}`,
      businessFlag: businessFlag,
      volume: vol ?? volume.value,
    };
  };

  // 优先买入逻辑，可能有两条订单信息
  if (currentTradeButton.value.label.includes('优先')) {
    /** 下单金额 */
    const amount = price.value * volume.value;
    /** 可用资金 */
    const availableAmount = activeAccount!.available || 0;
    /** 可用融资金额 */
    // const creditAvailable = activeAccount!.enableCreditBuy || 0; // todo25
    const creditAvailable = 0;
    /** 最小买入数量 */
    const minVolume = 100;

    if (currentTradeButton.value.label === '优先担保品买') {
      if (availableAmount > amount) {
        // 可用资金足够，生成一条普通买入订单
        orders.push(createOrder(BusinessFlagEnum.普通买卖));
      } else {
        // 可用资金不足，先使用普通买入最大可买数量，剩余使用融资买入
        const maxAvailableVolume = Math.floor(availableAmount / price.value / 100) * 100;
        if (maxAvailableVolume >= minVolume) {
          orders.push(createOrder(BusinessFlagEnum.普通买卖, maxAvailableVolume));
        }
        const remainingVolume = volume.value - maxAvailableVolume;
        if (remainingVolume >= minVolume) {
          orders.push(createOrder(BusinessFlagEnum.融资融券, remainingVolume));
        }
      }
    } else if (currentTradeButton.value.label === '优先融资买') {
      if (creditAvailable > amount) {
        // 融资可用金额足够，生成一条融资买入订单
        orders.push(createOrder(BusinessFlagEnum.融资融券));
      } else {
        // 融资可用金额不足，先使用融资买入最大可买数量，剩余使用普通买入
        const maxCreditVolume = Math.floor(creditAvailable / price.value / 100) * 100;
        if (maxCreditVolume >= minVolume) {
          orders.push(createOrder(BusinessFlagEnum.融资融券, maxCreditVolume));
        }
        const remainingVolume = volume.value - maxCreditVolume;
        if (remainingVolume >= minVolume) {
          orders.push(createOrder(BusinessFlagEnum.普通买卖, remainingVolume));
        }
      }
    }
  } else {
    orders.push(createOrder(currentTradeButton.value.businessFlag!));
  }

  return orders;
};

/** 提交订单 */
const submitOrders = async (orders: RegularOrder[]) => {
  console.log('Submit orders:', orders);
  orders.forEach(async order => {
    await TradingService.sendOrder(order);
    ElMessage.success('下单请求已发送');
  });
};

defineExpose({
  price,
  selectedInstrument,
});
</script>

<template>
  <div class="trade-panel" p="15" pr-0 flex="~ col" h-full>
    <!-- 代码输入 -->
    <div mb-15 flex aic>
      <div w-40>合约</div>
      <div flex-1>
        <InstrumentInput
          v-model="selectedInstrument"
          :assetType="activeChannel?.assetTypes?.[0]"
          placeholder="请输入合约代码或名称"
          w-full
        />
      </div>
    </div>

    <!-- 价格输入 -->
    <div mb-15 flex aic>
      <div w-40>价格</div>
      <div flex-1>
        <el-input-number
          v-model="price"
          :min="lastTick?.lowerLimitPrice || 0"
          :max="lastTick?.upperLimitPrice || 99999999"
          :step="selectedInstrument?.priceTick || 0.01"
          :precision="2"
          w="full!"
        />
      </div>
    </div>

    <!-- 价格限制信息 -->
    <div mb-15 pl-40>
      <div flex aic jcsb>
        <div flex aic v-for="(info, index) in priceInfos.slice(0, 2)" :key="index">
          <div>{{ info.label }}</div>
          <div ml-6 :class="info.color" cursor-pointer @click="info.onClick">
            {{ formatPrice(info.value) }}
          </div>
        </div>
      </div>
      <div flex aic jcsb>
        <div flex aic v-for="(info, index) in priceInfos.slice(2, 4)" :key="index + 2">
          <div>{{ info.label }}</div>
          <div ml-6 :class="info.color" cursor-pointer @click="info.onClick">
            {{ formatPrice(info.value) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 数量输入 -->
    <div mb-15 flex aic>
      <div w-40>数量</div>
      <div flex-1>
        <el-tooltip
          :offset="6"
          placement="top"
          effect="light"
          :visible="visible"
          :content="estimateCost"
        >
          <el-input-number v-model="volume" :min="0" :step="100" w="full!" />
        </el-tooltip>
      </div>
    </div>

    <!-- 快捷设置数量 -->
    <div flex mb-15>
      <div
        v-for="option in ratioOptions"
        :key="option.label"
        flex-1
        flex
        aic
        jcc
        h-25
        cursor-pointer
        @click="setVolumeByPositionPercent(option.value)"
      >
        {{ option.label }}
      </div>
    </div>

    <!-- 下单按钮 -->
    <!-- 融资融券交易 - 下拉按钮 -->
    <el-dropdown
      v-if="activeChannel.credit"
      split-button
      class="dropdown-button"
      @click="handleSubmitOrder()"
    >
      {{ currentTradeButton.label }}
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="button in tradeButtons"
            :key="button.label"
            @click="handleSelectTradeType(button)"
          >
            {{ button.label }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <!-- 普通交易 - 普通按钮 -->
    <el-button v-else :color="tradeBgColor" @click="handleSubmitOrder()">
      {{ currentTradeButton.label }}
    </el-button>
  </div>
</template>

<style scoped>
.trade-panel {
  .dropdown-button :deep(.el-button-group) {
    width: 100%;
    display: flex;
    .el-button {
      background-color: v-bind(tradeBgColor);
      border-color: v-bind(tradeBgColor);
      &:hover {
        background-color: v-bind(tradeHoverColor);
        border-color: v-bind(tradeHoverColor);
      }
      color: white;
      &:first-child {
        flex: 1;
      }
    }
  }
}
</style>
