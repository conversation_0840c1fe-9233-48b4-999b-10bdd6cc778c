<script setup lang="ts">
import { ref, useTemplateRef, computed } from 'vue';
import { ElMessage, type FormRules } from 'element-plus';
import { AdminService } from '@/api';
import type { MomUser, FormUser, MomRole, MomOrganization } from '../../../../xtrade-sdk/dist';
import { getUser, hasGlobalDataPermission, isNotNone } from '@/script';

const { user } = defineProps<{
  user?: MomUser;
}>();

// 表单校验规则
const rules: FormRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  fullName: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 8, max: 12, message: '长度8-12位', trigger: 'blur' },
    {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^]{8,}$/,
      message: '数字、大小写字母组合',
      trigger: 'blur',
    },
  ],
  email: [
    {
      required: true,
      message: '请输入邮箱',
      trigger: 'blur',
    },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
  ],
  phoneNo: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur',
    },
  ],
  orgId: [{ required: true, message: '请选择机构', trigger: 'change' }],
  roleId: [{ required: true, message: '请选择角色', trigger: 'change' }],
};

const formRef = useTemplateRef('formRef');

const form = ref<FormUser>({
  email: '',
  username: '',
  fullName: '',
  password: '',
  phoneNo: '',
  orgId: undefined,
  orgName: '',
  roleId: undefined,
  roleName: '',
  status: 1,
  ip: '',
  mac: '',
  userType: 0,
  validTime: undefined,
  qualifications: '',
});

const currentUser = getUser()!;
const hasGlobalDataScope = ref(hasGlobalDataPermission());
// 角色列表
const roles = ref<MomRole[]>([]);
// 当前用户有权限的角色列表
const myRoles = computed(() => {
  return hasGlobalDataScope.value
    ? roles.value
    : roles.value.filter(x => {
        return (
          (isNotNone(x.orgId) && x.orgId == currentUser.orgId) || x.userType >= currentUser.userType
        );
      });
});
// 机构列表
const orgs = ref<MomOrganization[]>([]);
// 当前用户有权限的机构列表
const myOrgs = computed(() => {
  return hasGlobalDataScope.value ? orgs.value : orgs.value.filter(x => x.id == currentUser.orgId);
});

// 是否编辑模式
const isEdit = computed(() => !!user);

// 初始化数据
const initData = async () => {
  // 加载角色和机构列表
  await loadRolesAndOrgs();

  if (user) {
    // 编辑模式，填充表单数据
    form.value = {
      username: user.username,
      fullName: user.fullName,
      password: '',
      email: user.email,
      phoneNo: user.phoneNo || '',
      orgId: user.orgId,
      orgName: user.orgName,
      roleId: user.roleId,
      status: user.status,
      ip: user.ip || '',
      mac: user.mac || '',
      userType: user.userType,
      validTime: user.validTime,
      qualifications: user.qualifications || '',
    };
  } else {
    // 新建模式，重置表单
    resetForm();
  }
};

// 加载角色和机构列表
const loadRolesAndOrgs = async () => {
  try {
    const [rolesData, orgsData] = await Promise.all([
      AdminService.getRoles(),
      AdminService.getOrgs(),
    ]);
    // 过滤掉超级管理员角色
    roles.value = rolesData.filter(role => role.id !== 1);
    orgs.value = orgsData;
  } catch (error) {
    console.error('加载角色和机构列表失败:', error);
    ElMessage.error('加载数据失败');
  }
};

// 重置表单
const resetForm = () => {
  form.value = {
    username: '',
    fullName: '',
    password: '',
    email: '',
    phoneNo: '',
    orgId: undefined,
    roleId: undefined,
    status: 1,
    ip: '',
    mac: '',
    userType: 0,
    validTime: undefined,
    qualifications: '',
  };
  formRef.value?.clearValidate();
};

// 提交表单
const validate = () => {
  return formRef.value
    ?.validate()
    .then(valid => {
      if (valid) {
        return true;
      } else {
        return false;
      }
    })
    .catch(() => {
      return false;
    });
};

const handleRoleChange = (roleId: number) => {
  const role = roles.value.find(role => role.id === roleId);
  if (role) {
    form.value.roleName = role.roleName;
    form.value.userType = role.userType;
  }
};

const handleOrgChange = (orgId: number) => {
  const org = orgs.value.find(org => org.id === orgId);
  if (org) {
    form.value.orgName = org.orgName;
  }
};

// 组件挂载时初始化数据
initData();

defineExpose({
  validate,
  form,
});
</script>

<template>
  <div p-6>
    <el-form ref="formRef" :model="form" :rules="rules" class="typical-form">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input v-model.trim="form.username" placeholder="请输入用户名">
              <template #prefix>
                <i class="iconfont icon-consist"></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="真实姓名" prop="fullName">
            <el-input v-model.trim="form.fullName" placeholder="请输入真实姓名">
              <template #prefix>
                <i class="iconfont icon-user"></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="角色" prop="roleId">
            <el-select
              v-model="form.roleId"
              placeholder="请选择角色"
              w-full
              @change="handleRoleChange"
            >
              <template #prefix>
                <i class="iconfont icon-role"></i>
              </template>
              <el-option
                v-for="role in myRoles"
                :key="role.id"
                :label="role.roleName"
                :value="role.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话" prop="phoneNo">
            <el-input v-model.trim="form.phoneNo" placeholder="请输入电话号码">
              <template #prefix>
                <i class="iconfont icon-telephone"></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="机构" prop="orgId">
            <el-select
              v-model="form.orgId"
              placeholder="请选择机构"
              w-full
              @change="handleOrgChange"
            >
              <template #prefix>
                <i class="iconfont icon-building"></i>
              </template>
              <el-option v-for="org in myOrgs" :key="org.id" :label="org.orgName" :value="org.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model.trim="form.email" placeholder="请输入邮箱">
              <template #prefix>
                <i class="iconfont icon-envelop"></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="有效期" prop="validTime">
            <el-date-picker
              v-model="form.validTime"
              placeholder="请选择有效期"
              value-format="x"
              w-full
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="从业资格" prop="qualifications">
            <el-input v-model.trim="form.qualifications" placeholder="请输入从业资格">
              <template #prefix>
                <i class="iconfont icon-document"></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<style scoped></style>
