/**
 * 产品管理
 */
export enum MenuPermitProductManagement {
  创建产品 = 'create',
  修改产品 = 'edit',
  删除产品 = 'delete',
  下载产品 = 'download',
  列配置 = 'column_config',
  产品权益查看 = 'view_equity',
  账号数据查看 = 'view_account_data',
  持仓数据查看 = 'view_position_data',
  成交数据查看 = 'view_trade_data',
  指令数据查看 = 'view_order_data',
  账号配置 = 'account_config',
  人员配置 = 'staff_config',
  风控配置 = 'risk_config',
  创建账号 = 'create_account',
}

/**
 * 产品概览
 */
export enum MenuPermitProductOverview {}

/**
 * 产品净值
 */
export enum MenuPermitProductNetValue {}

/**
 * 今日订单
 */
export enum MenuPermitTodayOrders {
  工具栏撤销全部 = 'toolbar_cancel_all',
  工具栏撤销全部买单 = 'toolbar_cancel_all_bids',
  工具栏撤销全部卖单 = 'toolbar_cancel_all_asks',
  工具栏撤勾选 = 'toolbar_cancel_selected',
  撤单条 = 'cancel_order',
  添加单条 = 'add_order',
  编辑单条 = 'edit_order',
  删除单条 = 'delete_order',
}

/**
 * 今日持仓
 */
export enum MenuPermitTodayPositions {
  添加调仓记录 = 'add_position_adjustment_record',
  添加单条 = 'add_position',
  编辑单条 = 'edit_position',
  删除单条 = 'delete_position',
}

/**
 * 今日成交
 */
export enum MenuPermitTodayTrades {
  添加单条 = 'add_trade',
  编辑单条 = 'edit_trade',
  删除单条 = 'delete_trade',
}

/**
 * 历史订单
 */
export enum MenuPermitHistoricalOrders {}

/**
 * 历史持仓
 */
export enum MenuPermitHistoricalPositions {}

/**
 * 历史成交
 */
export enum MenuPermitHistoricalTrades {}

/**
 * 历史权益
 */
export enum MenuPermitHistoricalEquity {}

/**
 * 账号管理
 */
export enum MenuPermitAccountManagement {
  创建账号 = 'create',
  创建虚拟账号 = 'create_virtual_account',
  修改账号 = 'edit',
  密码维护 = 'password_maintenance',
  配置终端 = 'set_terminal',
  删除账号 = 'delete',
  下载账号列表 = 'download',
  列配置 = 'column_config',
  连通性测试 = 'connect_disconnect_switch',
  查看权益 = 'view_equity',
  出入金管理 = 'fund_management',
  费用管理 = 'fee_management',
  清算 = 'settlement',
  可用资金检查 = 'available_fund_check',
  持仓检查 = 'position_check',
  持仓查看 = 'position_view',
  风控 = 'risk_control',
  权益维护 = 'equity_maintenance',
  对比覆盖 = 'compare_override',
  资金覆盖 = 'fund_override',
  同步合约 = 'sync_contract',
  初始化 = 'init_account',
  创建产品 = 'create_product',
}

/**
 * 出入金
 */
export enum MenuPermitFundInOut {
  添加 = 'add',
}

/**
 * 账号监控
 */
export enum MenuPermitAccountMonitoring {
  覆盖 = 'override',
  成交检测 = 'trade_detection',
  委托检测 = 'order_detection',
  资金检测 = 'fund_detection',
}

/**
 * 权益查询
 */
export enum MenuPermitEquityQuery {}

/**
 * 委托查询
 */
export enum MenuPermitOrderQuery {}

/**
 * 持仓查询
 */
export enum MenuPermitPositionQuery {}

/**
 * 成交查询
 */
export enum MenuPermitTradeQuery {}

/**
 * 普通交易
 */
export enum MenuPermitNormalTrading {
  现货竞价交易 = 'spot_auction_trading',
  信用交易 = 'credit_trading',
  可转债交易 = 'convertible_bond_trading',
  期货交易 = 'futures_trading',
}

/**
 * 批量交易
 */
export enum MenuPermitBatchTrading {
  现货竞价交易 = 'spot_auction_trading',
  信用交易 = 'credit_trading',
  可转债交易 = 'convertible_bond_trading',
  期货交易 = 'futures_trading',
}

/**
 * 篮子算法交易
 */
export enum MenuPermitBasketAlgorithmTrading {
  现货竞价交易 = 'spot_auction_trading',
  信用交易 = 'credit_trading',
  可转债交易 = 'convertible_bond_trading',
  期货交易 = 'futures_trading',
  创建篮子 = 'create_basket',
  删除篮子 = 'delete_basket',
  篮子调仓 = 'basket_rebalancing',
}

/**
 * 算法交易
 */
export enum MenuPermitAlgorithmTrading {
  现货竞价交易 = 'spot_auction_trading',
  信用交易 = 'credit_trading',
  可转债交易 = 'convertible_bond_trading',
  期货交易 = 'futures_trading',
  创建篮子 = 'create_basket',
  删除篮子 = 'delete_basket',
  篮子调仓 = 'basket_rebalancing',
}

/**
 * 文件扫单
 */
export enum MenuPermitFileScanning {}

/**
 * 待审核订单
 */
export enum MenuPermitPendingReviewOrders {
  全部通过 = 'approve_all',
  通过勾选 = 'approve_selected',
  全部驳回 = 'reject_all',
  驳回勾选 = 'reject_selected',
  通过 = 'approve',
  驳回 = 'reject',
}

/**
 * 已审核订单
 */
export enum MenuPermitReviewedOrders {}

/**
 * 已执行订单
 */
export enum MenuPermitExecutedOrders {}

/**
 * 流程管理
 */
export enum MenuPermitProcessManagement {
  创建 = 'create',
  编辑 = 'edit',
  删除 = 'delete',
}

/**
 * 机构管理
 */
export enum MenuPermitOrganizationManagement {
  创建 = 'create',
  编辑 = 'edit',
  删除 = 'delete',
  机构状态 = 'enable_disable',
}

/**
 * 角色管理
 */
export enum MenuPermitRoleManagement {
  创建 = 'create',
  编辑 = 'edit',
  删除 = 'delete',
  克隆角色 = 'clone_role',
  关联用户 = 'associate_user',
}

/**
 * 用户管理
 */
export enum MenuPermitUserManagement {
  创建 = 'create',
  编辑 = 'edit',
  删除 = 'delete',
  用户状态 = 'enable_disable',
  强制下线 = 'force_kick_out',
  重置密码 = 'reset_password',
  机器绑定 = 'machine_binding',
  产品权限 = 'product_permission',
  创建产品 = 'create_product',
  交易权限 = 'trading_permission',
  操作日志 = 'view_operation_log',
  登录日志 = 'view_login_log',
}

/**
 * 经纪商管理
 */
export enum MenuPermitBrokerManagement {
  创建 = 'create',
  编辑 = 'edit',
  删除 = 'delete',
}

/**
 * 终端管理
 */
export enum MenuPermitTerminalManagement {
  创建 = 'create',
  编辑 = 'edit',
  删除 = 'delete',
  终端状态 = 'enable_disable',
}

/**
 * 风控模版管理
 */
export enum MenuPermitRiskTemplateManagement {}

/**
 * 风控消息
 */
export enum MenuPermitRiskControlMessage {}

/**
 * 交易日
 */
export enum MenuPermitTradingDay {}

/**
 * 运维
 */
export enum MenuPermitOperationMaintenance {}
