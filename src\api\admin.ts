import type { Role<PERSON>enuPermission } from '@/types';

import {
  <PERSON>os,
  type <PERSON><PERSON>enu,
  type <PERSON><PERSON>enu,
  type <PERSON>R<PERSON>,
  type <PERSON>R<PERSON>,
  type MomPermission,
  type FormPermission,
  type MomUser,
  type FormUser,
  type UserPasswordReset,
  type UserPasswordChange,
  type MomOrganization,
  type FormOrganization,
  type MomBroker,
  type FormBroker,
  type MomTerminal,
  type FormTerminal,
  type MomWorkflow,
  type FormWorkflow,
} from '../../../xtrade-sdk/dist';

const adminRepo = new Repos.AdminRepo();

class AdminService {
  // ==================== 用户管理相关方法 ====================

  static async getUsers() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryUsers();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async createUser(user: FormUser) {
    return adminRepo.CreateUser(user);
  }

  static async updateUser(user: MomUser) {
    return adminRepo.UpdateUser(user);
  }

  static async deleteUser(user_id: number) {
    return adminRepo.DeleteUser(user_id);
  }

  static async forceLogoutUser(user_id: number) {
    return adminRepo.ForceLogoutUser(user_id);
  }

  static async resetUserPassword(data: UserPasswordReset) {
    return adminRepo.ResetUserPassword(data);
  }

  static async changeUserPassword(data: UserPasswordChange) {
    return adminRepo.ChangeUserPassword(data);
  }

  // ==================== 机构管理相关方法 ====================

  static async getOrgs() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryOrgs();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async createOrg(org: FormOrganization) {
    return adminRepo.CreateOrg(org);
  }

  static async updateOrg(org: MomOrganization) {
    return adminRepo.UpdateOrg(org);
  }

  static async deleteOrg(org_id: number) {
    return adminRepo.DeleteOrg(org_id);
  }

  static async getRoles() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryRoles();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async createRole(role: FormRole) {
    return adminRepo.CreateRole(role);
  }

  static async updateRole(role: MomRole) {
    return adminRepo.UpdateRole(role);
  }

  static async deleteRole(role_id: number) {
    return adminRepo.DeleteRole(role_id);
  }

  static async getMenuTree() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryMenuTree();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async getRoleMenuTree(roleId: number) {
    const { errorCode, errorMsg, data } = await adminRepo.QueryRoleMenu(roleId);
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async saveRoleMenuPermissions(role_id: number, data: RoleMenuPermission[]) {
    return adminRepo.SaveRoleMenuPermissions(role_id, data);
  }

  // ==================== 经纪商管理相关方法 ====================

  static async getBrokers() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryBrokers();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async createBroker(broker: FormBroker) {
    return adminRepo.CreateBroker(broker);
  }

  static async updateBroker(broker: MomBroker) {
    return adminRepo.UpdateBroker(broker);
  }

  static async deleteBroker(broker_id: number) {
    return adminRepo.DeleteBroker(broker_id);
  }

  // ==================== 终端管理相关方法 ====================

  static async getTerminals() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryTerminals();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async createTerminal(terminal: FormTerminal) {
    return adminRepo.CreateTerminal(terminal);
  }

  static async updateTerminal(terminal: MomTerminal) {
    return adminRepo.UpdateTerminal(terminal);
  }

  static async deleteTerminal(terminal_id: number) {
    return adminRepo.DeleteTerminal(terminal_id);
  }

  static async getTerminalAccounts(terminal_id: number) {
    const { errorCode, errorMsg, data } = await adminRepo.QueryTerminalAccounts(terminal_id);
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  // ==================== 菜单管理相关方法 ====================

  static async createMenu(menu: FormMenu) {
    return adminRepo.CreateMenu(menu);
  }

  static async updateMenu(menu: MomMenu) {
    return adminRepo.UpdateMenu(menu);
  }

  static async deleteMenu(menu_id: number) {
    return adminRepo.DeleteMenu(menu_id);
  }

  // ==================== 权限管理相关方法 ====================

  static async getPermissionsByMenu(menu_id: number) {
    const { errorCode, errorMsg, data } = await adminRepo.QueryPermissionsByMenu(menu_id);
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async createPermission(permission: FormPermission) {
    return adminRepo.CreatePermission(permission);
  }

  static async updatePermission(permission: MomPermission) {
    return adminRepo.UpdatePermission(permission);
  }

  static async deletePermission(permission_id: number) {
    return adminRepo.DeletePermission(permission_id);
  }

  // ==================== 流程管理相关方法 ====================

  static async getProcedures(workflow_id?: number) {
    const { errorCode, errorMsg, data } = await adminRepo.QueryProcedure(workflow_id);
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async createProcedure(workflow: FormWorkflow) {
    return adminRepo.CreateProcedure(workflow);
  }

  static async updateProcedure(workflow: MomWorkflow) {
    return adminRepo.UpdateProcedure(workflow);
  }

  static async deleteProcedure(workflow_id: number) {
    return adminRepo.DeleteProcedure(workflow_id);
  }

  static async bindProcedure(account_id: number, workflow_id?: number) {
    return adminRepo.BindProcedure(account_id, workflow_id);
  }

  // ==================== 日志管理相关方法 ====================

  static async getActionLogs(log_type: number, op_user_id: number) {
    return adminRepo.QueryActionLogs(log_type, op_user_id);
  }
}

export default AdminService;
